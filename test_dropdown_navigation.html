<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Navigation - ABLA</title>
    <link rel="stylesheet" href="https://unpkg.com/@phosphor-icons/web@2.0.3/src/regular/style.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #090C14;
            color: #FFFFFF;
            line-height: 1.6;
        }
        
        header {
            background: #13161F;
            padding: 1rem 0;
            border-bottom: 1px solid #333;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            font-size: 1.25rem;
            font-weight: bold;
            color: #F7931A;
        }
        
        .desktop-nav {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        .nav-link {
            color: #FFFFFF;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: #F7931A;
        }
        
        /* Dropdown Styles */
        .group {
            position: relative;
        }
        
        .group .absolute {
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
            width: 16rem;
            background: #1F2937;
            border: 1px solid #374151;
            border-radius: 0.5rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            z-index: 50;
            pointer-events: none;
        }
        
        .group:hover .absolute {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }
        
        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #D1D5DB;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }
        
        .dropdown-link:hover {
            color: #F7931A;
            background-color: #374151;
            transform: translateX(4px);
        }
        
        .content {
            margin-top: 80px;
            padding: 2rem 1rem;
        }
        
        .section {
            min-height: 100vh;
            padding: 4rem 0;
            border-bottom: 1px solid #333;
        }
        
        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #F7931A;
        }
        
        .test-info {
            background: #13161F;
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            border: 1px solid #333;
        }
        
        .status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: bold;
            margin: 0.5rem;
        }
        
        .status.success {
            background: #10B981;
            color: white;
        }
        
        .status.error {
            background: #EF4444;
            color: white;
        }
        
        .status.info {
            background: #3B82F6;
            color: white;
        }
        
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">ABLA TEST</div>
            
            <!-- Desktop Navigation with Dropdown -->
            <nav class="desktop-nav">
                <a href="#inicio" class="nav-link">
                    <i class="ph ph-house"></i>INICIO
                </a>
                
                <!-- Nosotros Dropdown Menu -->
                <div class="group">
                    <button class="nav-link">
                        <i class="ph ph-users-three"></i>NOSOTROS
                        <i class="ph ph-caret-down ml-1"></i>
                    </button>
                    
                    <!-- Dropdown Content -->
                    <div class="absolute">
                        <div class="py-2">
                            <a href="#manifesto" class="dropdown-link">
                                <i class="ph ph-scroll mr-3 text-orange-500"></i>
                                <div>
                                    <div class="font-medium">Manifiesto</div>
                                    <div class="text-sm text-gray-400">Nuestros principios</div>
                                </div>
                            </a>
                            <a href="#por-que-abla" class="dropdown-link">
                                <i class="ph ph-question mr-3 text-orange-500"></i>
                                <div>
                                    <div class="font-medium">¿Por qué ABLA?</div>
                                    <div class="text-xs text-gray-400">Nuestra razón de ser</div>
                                </div>
                            </a>
                            <a href="#estatutos" class="dropdown-link">
                                <i class="ph ph-file-text mr-3 text-orange-500"></i>
                                <div>
                                    <div class="font-medium">Estatutos</div>
                                    <div class="text-xs text-gray-400">Estructura y gobernanza</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                
                <a href="#contacto" class="nav-link">
                    <i class="ph ph-envelope"></i>CONTACTO
                </a>
            </nav>
        </div>
    </header>
    
    <div class="content">
        <section id="inicio" class="section">
            <div class="container">
                <h2>Test de Navegación Dropdown</h2>
                
                <div class="test-info">
                    <h3>Instrucciones de Prueba</h3>
                    <p><strong>Problema a verificar:</strong> Cuando se hace clic en un enlace del dropdown "NOSOTROS", la página debe navegar al destino y permanecer allí, sin regresar automáticamente al inicio.</p>
                    
                    <h4>Pasos para probar:</h4>
                    <ol>
                        <li>Haz hover sobre "NOSOTROS" en la navegación superior</li>
                        <li>Haz clic en "Manifesto" en el dropdown</li>
                        <li>Observa si la página se desplaza a la sección Manifesto</li>
                        <li>Verifica que la página permanezca en la sección Manifesto (no debe regresar al inicio)</li>
                        <li>Repite con "¿Por qué ABLA?" y "Estatutos"</li>
                    </ol>
                    
                    <div id="test-status" class="status info">Estado: Listo para probar</div>
                </div>
            </div>
        </section>
        
        <section id="manifesto" class="section">
            <div class="container">
                <h2>Manifiesto</h2>
                <div class="test-info">
                    <h3>✅ Sección Manifesto</h3>
                    <p>Si puedes leer esto y la página no regresó automáticamente al inicio, entonces la navegación del dropdown está funcionando correctamente.</p>
                    <div class="status success">Navegación exitosa al Manifesto</div>
                </div>
                <p>Contenido del manifesto de ABLA...</p>
            </div>
        </section>
        
        <section id="por-que-abla" class="section">
            <div class="container">
                <h2>¿Por qué ABLA?</h2>
                <div class="test-info">
                    <h3>✅ Sección ¿Por qué ABLA?</h3>
                    <p>Si puedes leer esto y la página no regresó automáticamente al inicio, entonces la navegación del dropdown está funcionando correctamente.</p>
                    <div class="status success">Navegación exitosa a ¿Por qué ABLA?</div>
                </div>
                <p>Explicación de por qué existe ABLA...</p>
            </div>
        </section>
        
        <section id="estatutos" class="section">
            <div class="container">
                <h2>Estatutos</h2>
                <div class="test-info">
                    <h3>✅ Sección Estatutos</h3>
                    <p>Si puedes leer esto y la página no regresó automáticamente al inicio, entonces la navegación del dropdown está funcionando correctamente.</p>
                    <div class="status success">Navegación exitosa a Estatutos</div>
                </div>
                <p>Estatutos y estructura organizativa de ABLA...</p>
            </div>
        </section>
        
        <section id="contacto" class="section">
            <div class="container">
                <h2>Contacto</h2>
                <div class="test-info">
                    <h3>✅ Sección Contacto</h3>
                    <p>Navegación directa (sin dropdown) funcionando correctamente.</p>
                    <div class="status success">Navegación exitosa a Contacto</div>
                </div>
                <p>Información de contacto de ABLA...</p>
            </div>
        </section>
    </div>
    
    <script>
        // Enhanced smooth scrolling with dropdown handling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    // Close any open dropdowns first
                    const dropdowns = document.querySelectorAll('.group');
                    dropdowns.forEach(dropdown => {
                        dropdown.classList.remove('dropdown-open');
                    });
                    
                    // Update test status
                    const testStatus = document.getElementById('test-status');
                    if (testStatus) {
                        testStatus.textContent = `Navegando a ${targetId}...`;
                        testStatus.className = 'status info';
                    }
                    
                    // Small delay to ensure dropdown closes before scrolling
                    setTimeout(() => {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                        
                        // Update URL hash after scrolling
                        setTimeout(() => {
                            if (history.pushState) {
                                history.pushState(null, null, targetId);
                            } else {
                                location.hash = targetId;
                            }
                            
                            // Update test status
                            if (testStatus) {
                                testStatus.textContent = `✅ Navegación exitosa a ${targetId}`;
                                testStatus.className = 'status success';
                            }
                        }, 100);
                    }, 50);
                }
            });
        });
        
        // Enhanced dropdown menu handling
        const dropdownGroups = document.querySelectorAll('.group');
        dropdownGroups.forEach(group => {
            const dropdownContent = group.querySelector('.absolute');
            const dropdownLinks = group.querySelectorAll('a[href^="#"]');
            
            // Handle dropdown link clicks
            dropdownLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Force close dropdown immediately
                    if (dropdownContent) {
                        dropdownContent.style.opacity = '0';
                        dropdownContent.style.visibility = 'hidden';
                    }
                    
                    // Remove hover state from parent
                    group.classList.remove('group-hover');
                    
                    // Ensure no interference with scrolling
                    setTimeout(() => {
                        if (dropdownContent) {
                            dropdownContent.style.opacity = '';
                            dropdownContent.style.visibility = '';
                        }
                    }, 1000);
                });
            });
        });
        
        // Monitor scroll position to detect unwanted scrolling
        let lastScrollPosition = 0;
        let scrollTimeout;
        
        window.addEventListener('scroll', () => {
            const currentPosition = window.pageYOffset;
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                // Check if we unexpectedly scrolled back to top
                if (lastScrollPosition > 100 && currentPosition < 50) {
                    const testStatus = document.getElementById('test-status');
                    if (testStatus) {
                        testStatus.textContent = '❌ Detectado scroll automático al inicio - BUG PRESENTE';
                        testStatus.className = 'status error';
                    }
                }
                lastScrollPosition = currentPosition;
            }, 100);
        });
    </script>
</body>
</html>
