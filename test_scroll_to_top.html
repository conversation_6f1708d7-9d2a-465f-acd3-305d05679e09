<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Scroll to Top Button - ABLA</title>
    <link rel="stylesheet" href="https://unpkg.com/@phosphor-icons/web@2.0.3/src/regular/style.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #090C14;
            color: #FFFFFF;
            line-height: 1.6;
        }
        
        .section {
            min-height: 100vh;
            padding: 4rem 2rem;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .section h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
            color: #F7931A;
            text-align: center;
        }
        
        .section p {
            max-width: 800px;
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .test-info {
            background: #13161F;
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            border: 1px solid #333;
            max-width: 600px;
        }
        
        .status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: bold;
            margin: 0.5rem;
        }
        
        .status.success {
            background: #10B981;
            color: white;
        }
        
        .status.info {
            background: #3B82F6;
            color: white;
        }
        
        .status.warning {
            background: #F59E0B;
            color: white;
        }
        
        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3.5rem;
            height: 3.5rem;
            background: linear-gradient(135deg, #F7931A 0%, #E8851C 100%);
            border: none;
            border-radius: 50%;
            color: #FFFFFF;
            font-size: 1.25rem;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px) scale(0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            outline: none;
            user-select: none;
        }
        
        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }
        
        .scroll-to-top:hover {
            background: linear-gradient(135deg, #E8851C 0%, #D4771A 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(247, 147, 26, 0.4), 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .scroll-to-top:active {
            transform: translateY(0) scale(0.95);
        }
        
        .scroll-to-top:focus {
            outline: 2px solid #F7931A;
            outline-offset: 2px;
        }
        
        .scroll-to-top i {
            transition: transform 0.2s ease;
        }
        
        .scroll-to-top:hover i {
            transform: translateY(-1px);
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: #13161F;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #333;
            font-family: monospace;
            font-size: 0.875rem;
            z-index: 999;
        }
        
        @media (max-width: 768px) {
            .scroll-to-top {
                bottom: 1.5rem;
                right: 1.5rem;
                width: 3rem;
                height: 3rem;
                font-size: 1.125rem;
            }
        }
    </style>
</head>
<body>
    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
        <div>Scroll Position: <span id="scrollPos">0</span>px</div>
        <div>Viewport Height: <span id="viewportHeight">0</span>px</div>
        <div>Button Status: <span id="buttonStatus">Hidden</span></div>
    </div>
    
    <!-- Scroll to Top Button -->
    <button 
        id="scrollToTop" 
        class="scroll-to-top" 
        aria-label="Volver al inicio de la página"
        title="Volver arriba"
        type="button"
    >
        <i class="ph ph-arrow-up" aria-hidden="true"></i>
    </button>
    
    <section class="section" style="background: linear-gradient(135deg, #1a1a2e, #16213e);">
        <h2>🚀 Scroll to Top Button Test</h2>
        <div class="test-info">
            <h3>Test Instructions</h3>
            <p><strong>Purpose:</strong> Test the floating scroll-to-top button functionality on the ABLA website.</p>
            
            <h4>Expected Behavior:</h4>
            <ul style="text-align: left; margin: 1rem 0;">
                <li>✅ Button should be <strong>hidden</strong> when at the top of the page</li>
                <li>✅ Button should <strong>appear</strong> after scrolling past 100vh (one viewport height)</li>
                <li>✅ Button should have <strong>smooth fade-in/fade-out</strong> animations</li>
                <li>✅ Clicking the button should <strong>smoothly scroll to top</strong></li>
                <li>✅ Button should be <strong>accessible</strong> via keyboard (Tab + Enter/Space)</li>
                <li>✅ Button should have <strong>hover effects</strong> and proper styling</li>
            </ul>
            
            <div class="status info">Ready to test - Scroll down to see the button appear!</div>
        </div>
    </section>
    
    <section class="section" style="background: linear-gradient(135deg, #16213e, #0f3460);">
        <h2>📱 Section 2 - Mobile Test</h2>
        <p>This section tests the scroll-to-top button on mobile devices. The button should be appropriately sized and positioned for touch interaction.</p>
        <div class="test-info">
            <h4>Mobile Specifications:</h4>
            <ul style="text-align: left;">
                <li>Minimum 44px touch target (accessibility standard)</li>
                <li>Proper spacing from viewport edges</li>
                <li>Responsive sizing for different screen sizes</li>
                <li>Touch-friendly hover states</li>
            </ul>
            <div class="status success">Button should be visible now!</div>
        </div>
    </section>
    
    <section class="section" style="background: linear-gradient(135deg, #0f3460, #533a7d);">
        <h2>🎨 Section 3 - Design Test</h2>
        <p>This section verifies the visual design and branding consistency of the scroll-to-top button.</p>
        <div class="test-info">
            <h4>Design Requirements:</h4>
            <ul style="text-align: left;">
                <li>Orange gradient background (#F7931A to #E8851C)</li>
                <li>Circular button design</li>
                <li>Phosphor Icons arrow-up icon</li>
                <li>Smooth transitions and hover effects</li>
                <li>Proper shadows and depth</li>
                <li>High contrast for accessibility</li>
            </ul>
            <div class="status success">Design elements should be visible</div>
        </div>
    </section>
    
    <section class="section" style="background: linear-gradient(135deg, #533a7d, #7b2cbf);">
        <h2>⚡ Section 4 - Performance Test</h2>
        <p>This section tests the performance and smooth scrolling behavior of the button.</p>
        <div class="test-info">
            <h4>Performance Checks:</h4>
            <ul style="text-align: left;">
                <li>Smooth scroll animation (not instant jump)</li>
                <li>No conflicts with existing navigation</li>
                <li>Proper event handling and throttling</li>
                <li>Efficient scroll position detection</li>
                <li>No layout shifts or visual glitches</li>
            </ul>
            <div class="status warning">Test smooth scrolling by clicking the button</div>
        </div>
    </section>
    
    <section class="section" style="background: linear-gradient(135deg, #7b2cbf, #f72585);">
        <h2>♿ Section 5 - Accessibility Test</h2>
        <p>This section verifies the accessibility features of the scroll-to-top button.</p>
        <div class="test-info">
            <h4>Accessibility Features:</h4>
            <ul style="text-align: left;">
                <li>Proper ARIA labels and descriptions</li>
                <li>Keyboard navigation support (Tab to focus)</li>
                <li>Enter and Space key activation</li>
                <li>Screen reader compatibility</li>
                <li>Sufficient color contrast</li>
                <li>Focus indicators</li>
            </ul>
            <div class="status info">Use Tab key to focus the button, then press Enter or Space</div>
        </div>
    </section>
    
    <section class="section" style="background: linear-gradient(135deg, #f72585, #F7931A);">
        <h2>✅ Section 6 - Final Test</h2>
        <p>Final verification that all scroll-to-top button features are working correctly.</p>
        <div class="test-info">
            <h4>Final Checklist:</h4>
            <ul style="text-align: left;">
                <li>✅ Button appears after scrolling past first viewport</li>
                <li>✅ Button disappears when near the top</li>
                <li>✅ Smooth scrolling animation works</li>
                <li>✅ Hover effects are smooth and responsive</li>
                <li>✅ Keyboard accessibility works</li>
                <li>✅ Mobile responsiveness is correct</li>
                <li>✅ Design matches ABLA branding</li>
            </ul>
            <div class="status success">All tests completed - Click the button to return to top!</div>
        </div>
    </section>
    
    <script>
        // Scroll to Top Button Functionality
        const scrollToTopButton = document.getElementById('scrollToTop');
        const scrollPosElement = document.getElementById('scrollPos');
        const viewportHeightElement = document.getElementById('viewportHeight');
        const buttonStatusElement = document.getElementById('buttonStatus');
        
        let isScrolling = false;
        let scrollTimeout;

        // Update scroll indicator
        const updateScrollIndicator = () => {
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight;
            
            scrollPosElement.textContent = Math.round(scrollPosition);
            viewportHeightElement.textContent = viewportHeight;
        };

        // Show/hide scroll to top button based on scroll position
        const toggleScrollToTopButton = () => {
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight;
            
            updateScrollIndicator();
            
            if (scrollPosition > viewportHeight) {
                if (!scrollToTopButton.classList.contains('visible')) {
                    scrollToTopButton.classList.add('visible');
                    scrollToTopButton.classList.remove('hidden');
                    buttonStatusElement.textContent = 'Visible';
                    buttonStatusElement.style.color = '#10B981';
                }
            } else {
                if (scrollToTopButton.classList.contains('visible')) {
                    scrollToTopButton.classList.remove('visible');
                    scrollToTopButton.classList.add('hidden');
                    buttonStatusElement.textContent = 'Hidden';
                    buttonStatusElement.style.color = '#6B7280';
                }
            }
        };

        // Smooth scroll to top functionality
        const scrollToTop = () => {
            if (isScrolling) return;
            
            isScrolling = true;
            buttonStatusElement.textContent = 'Scrolling...';
            buttonStatusElement.style.color = '#F59E0B';
            
            // Add active state
            scrollToTopButton.style.transform = 'translateY(0) scale(0.95)';
            
            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Reset button state after animation
            setTimeout(() => {
                scrollToTopButton.style.transform = '';
                isScrolling = false;
                buttonStatusElement.textContent = 'Hidden';
                buttonStatusElement.style.color = '#6B7280';
            }, 600);
        };

        // Event listeners for scroll to top button
        if (scrollToTopButton) {
            // Click event
            scrollToTopButton.addEventListener('click', scrollToTop);
            
            // Keyboard accessibility
            scrollToTopButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    scrollToTop();
                }
            });
            
            // Scroll event listener with throttling
            window.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(toggleScrollToTopButton, 10);
            }, { passive: true });
            
            // Initial check
            toggleScrollToTopButton();
        }
        
        // Update viewport height on resize
        window.addEventListener('resize', updateScrollIndicator);
        
        // Initial update
        updateScrollIndicator();
    </script>
</body>
</html>
