<?php
/**
 * API endpoint for paginated initiatives
 * Supports AJAX requests for smooth pagination
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config.php';
require_once '../includes/db.php';
require_once '../includes/social_media_detector.php';
require_once '../includes/avatar_fetcher.php';

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = isset($_GET['per_page']) ? min(50, max(1, intval($_GET['per_page']))) : 6;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Calculate offset
$offset = ($page - 1) * $per_page;

// Connect to database
$db = connectDB();

if (is_array($db)) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed',
        'data' => []
    ]);
    exit;
}

try {
    // Build WHERE clause for search
    $where_clause = "WHERE 1=1";
    $params = [];
    $param_types = "";

    if (!empty($search)) {
        $where_clause .= " AND (name LIKE ? OR description LIKE ? OR instagram_user LIKE ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param];
        $param_types = "sss";
    }
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM initiatives $where_clause";
    if (!empty($params)) {
        $count_stmt = $db->prepare($count_query);
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total_result = $count_stmt->get_result();
        $total_initiatives = $total_result->fetch_assoc()['total'];
        $count_stmt->close();
    } else {
        $total_result = $db->query($count_query);
        $total_initiatives = $total_result->fetch_assoc()['total'];
    }
    
    $total_pages = ceil($total_initiatives / $per_page);
    
    // Get initiatives for current page
    $query = "SELECT * FROM initiatives $where_clause ORDER BY id DESC LIMIT ? OFFSET ?";
    $all_params = array_merge($params, [$per_page, $offset]);
    $all_param_types = $param_types . "ii";
    
    $stmt = $db->prepare($query);
    $stmt->bind_param($all_param_types, ...$all_params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $initiatives = [];
    while ($row = $result->fetch_assoc()) {
        // Get avatar URL
        $avatar_url = getAvatarForDisplay($row);
        
        // Get social media links
        $social_links = generateSocialMediaLinks($row);
        
        $initiatives[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'description' => $row['description'],
            'avatar_url' => $avatar_url,
            'social_links' => $social_links,
            'created_at' => $row['created_at']
        ];
    }
    
    $stmt->close();
    $db->close();
    
    // Return paginated response
    echo json_encode([
        'success' => true,
        'data' => [
            'initiatives' => $initiatives,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $per_page,
                'total_pages' => $total_pages,
                'total_items' => $total_initiatives,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $total_pages ? $page + 1 : null
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
