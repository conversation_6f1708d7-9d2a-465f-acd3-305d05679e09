<?php
// SEO Management Page
// Check if user is logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /?route=admin');
    exit;
}

require_once '../includes/seo.php';

$message = '';
$success = false;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_seo_settings'])) {
        // Update SEO settings
        $settings = [
            'default_meta_title' => $_POST['default_meta_title'] ?? '',
            'default_meta_description' => $_POST['default_meta_description'] ?? '',
            'default_meta_keywords' => $_POST['default_meta_keywords'] ?? '',
            'site_name' => $_POST['site_name'] ?? '',
            'twitter_handle' => $_POST['twitter_handle'] ?? '',
            'facebook_page' => $_POST['facebook_page'] ?? '',
            'google_analytics_id' => $_POST['google_analytics_id'] ?? '',
            'google_search_console' => $_POST['google_search_console'] ?? '',
            'robots_txt_custom' => $_POST['robots_txt_custom'] ?? '',
            'sitemap_enabled' => isset($_POST['sitemap_enabled']) ? '1' : '0'
        ];
        
        $updated = 0;
        foreach ($settings as $key => $value) {
            $stmt = $db->prepare("INSERT INTO seo_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
            $stmt->bind_param("sss", $key, $value, $value);
            if ($stmt->execute()) {
                $updated++;
            }
            $stmt->close();
        }
        
        if ($updated > 0) {
            $success = true;
            $message = "Configuración SEO actualizada correctamente ($updated configuraciones).";
        } else {
            $message = "Error al actualizar la configuración SEO.";
        }
    }
    
    if (isset($_POST['update_content_seo'])) {
        // Update content SEO
        $content_id = $_POST['content_id'] ?? '';
        $url_slug = trim($_POST['url_slug'] ?? '');
        $meta_title = $_POST['meta_title'] ?? '';
        $meta_description = $_POST['meta_description'] ?? '';
        $meta_keywords = $_POST['meta_keywords'] ?? '';
        $is_published = isset($_POST['is_published']) ? 1 : 0;
        
        if (empty($content_id)) {
            $message = "ID de contenido requerido.";
        } else {
            // Validate slug
            if (!empty($url_slug)) {
                $slug_errors = validateSlug($url_slug);
                if (!empty($slug_errors)) {
                    $message = "Error en URL slug: " . implode(', ', $slug_errors);
                } elseif (!isSlugUnique($url_slug, 'content', $content_id)) {
                    $message = "El URL slug '$url_slug' ya está en uso.";
                } else {
                    // Calculate SEO score
                    $seo_data = [
                        'title' => $meta_title,
                        'meta_description' => $meta_description,
                        'meta_keywords' => $meta_keywords,
                        'url_slug' => $url_slug,
                        'is_published' => $is_published
                    ];
                    
                    // Get content for score calculation
                    $content_stmt = $db->prepare("SELECT content FROM content WHERE id = ?");
                    $content_stmt->bind_param("i", $content_id);
                    $content_stmt->execute();
                    $content_result = $content_stmt->get_result();
                    if ($content_row = $content_result->fetch_assoc()) {
                        $seo_data['content'] = $content_row['content'];
                    }
                    $content_stmt->close();
                    
                    $seo_score = calculateSEOScore($seo_data);
                    
                    // Update content
                    $stmt = $db->prepare("UPDATE content SET url_slug = ?, meta_title = ?, meta_description = ?, meta_keywords = ?, is_published = ?, seo_score = ? WHERE id = ?");
                    $stmt->bind_param("ssssiii", $url_slug, $meta_title, $meta_description, $meta_keywords, $is_published, $seo_score, $content_id);
                    
                    if ($stmt->execute()) {
                        $success = true;
                        $message = "SEO del contenido actualizado correctamente. Puntuación SEO: $seo_score/100";
                    } else {
                        $message = "Error al actualizar SEO del contenido: " . $db->error;
                    }
                    $stmt->close();
                }
            } else {
                $message = "URL slug es requerido.";
            }
        }
    }
    
    if (isset($_POST['generate_slug'])) {
        // Generate slug for content
        $content_id = $_POST['content_id'] ?? '';
        $title = $_POST['title'] ?? '';
        
        if (!empty($title)) {
            $generated_slug = generateUniqueSlug($title, 'content', $content_id);
            echo json_encode(['success' => true, 'slug' => $generated_slug]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Título requerido']);
        }
        exit;
    }
}

// Get SEO settings
$seo_settings = getSEOSettings();

// Get content with SEO data
$content_seo = [];
try {
    $result = $db->query("SELECT id, section, title, url_slug, meta_title, meta_description, meta_keywords, is_published, seo_score FROM content ORDER BY section, title");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $content_seo[] = $row;
        }
    }
} catch (Exception $e) {
    $message = "Error al obtener datos SEO: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión SEO - ABLA Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <style>
        .seo-score-excellent { color: #10b981; }
        .seo-score-good { color: #f59e0b; }
        .seo-score-poor { color: #ef4444; }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold">Gestión SEO</h1>
                <p class="text-gray-400">Configuración de URLs amigables y optimización SEO</p>
            </div>
            <a href="/?route=admin" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded">
                <i class="ph ph-arrow-left mr-2"></i>Volver al Dashboard
            </a>
        </div>

        <!-- Messages -->
        <?php if (!empty($message)): ?>
        <div class="mb-6 p-4 rounded-lg <?php echo $success ? 'bg-green-600' : 'bg-red-600'; ?>">
            <div class="flex items-center">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- SEO Settings -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i class="ph ph-gear text-orange-500 mr-2"></i>
                Configuración SEO Global
            </h2>
            
            <form method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Título Meta por Defecto</label>
                        <input type="text" name="default_meta_title" 
                               value="<?php echo htmlspecialchars($seo_settings['default_meta_title'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="ABLA - Asociación Bitcoin Latinoamérica">
                        <p class="text-xs text-gray-400 mt-1">Recomendado: 50-60 caracteres</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Nombre del Sitio</label>
                        <input type="text" name="site_name" 
                               value="<?php echo htmlspecialchars($seo_settings['site_name'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="ABLA">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Descripción Meta por Defecto</label>
                    <textarea name="default_meta_description" rows="3"
                              class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                              placeholder="Descripción que aparecerá en los resultados de búsqueda"><?php echo htmlspecialchars($seo_settings['default_meta_description'] ?? ''); ?></textarea>
                    <p class="text-xs text-gray-400 mt-1">Recomendado: 150-160 caracteres</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Palabras Clave por Defecto</label>
                    <input type="text" name="default_meta_keywords" 
                           value="<?php echo htmlspecialchars($seo_settings['default_meta_keywords'] ?? ''); ?>"
                           class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                           placeholder="bitcoin, latinoamérica, criptomonedas">
                    <p class="text-xs text-gray-400 mt-1">Separar con comas</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Handle de Twitter</label>
                        <input type="text" name="twitter_handle" 
                               value="<?php echo htmlspecialchars($seo_settings['twitter_handle'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="@ABLALatam">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Página de Facebook</label>
                        <input type="url" name="facebook_page" 
                               value="<?php echo htmlspecialchars($seo_settings['facebook_page'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="https://facebook.com/ABLALatam">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Google Analytics ID</label>
                        <input type="text" name="google_analytics_id" 
                               value="<?php echo htmlspecialchars($seo_settings['google_analytics_id'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="G-XXXXXXXXXX">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Google Search Console</label>
                        <input type="text" name="google_search_console" 
                               value="<?php echo htmlspecialchars($seo_settings['google_search_console'] ?? ''); ?>"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="Código de verificación">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Robots.txt Personalizado</label>
                    <textarea name="robots_txt_custom" rows="4"
                              class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500 font-mono text-sm"
                              placeholder="Contenido adicional para robots.txt"><?php echo htmlspecialchars($seo_settings['robots_txt_custom'] ?? ''); ?></textarea>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="sitemap_enabled" id="sitemap_enabled" 
                           <?php echo ($seo_settings['sitemap_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>
                           class="mr-2">
                    <label for="sitemap_enabled" class="text-sm text-gray-300">Habilitar generación automática de sitemap</label>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" name="update_seo_settings" 
                            class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-6 rounded-lg">
                        <i class="ph ph-floppy-disk mr-2"></i>Guardar Configuración
                    </button>
                </div>
            </form>
        </div>

        <!-- Content SEO Management -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i class="ph ph-link text-orange-500 mr-2"></i>
                URLs Amigables y SEO por Contenido
            </h2>

            <?php if (empty($content_seo)): ?>
            <div class="text-center py-8">
                <i class="ph ph-file-text text-gray-500 text-4xl mb-4"></i>
                <p class="text-gray-400">No hay contenido para gestionar</p>
                <a href="/?route=admin" class="text-orange-500 hover:text-orange-400">Crear contenido primero</a>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-700">
                            <th class="text-left py-3 px-2">Contenido</th>
                            <th class="text-left py-3 px-2">URL Slug</th>
                            <th class="text-left py-3 px-2">Meta Título</th>
                            <th class="text-left py-3 px-2">SEO Score</th>
                            <th class="text-left py-3 px-2">Estado</th>
                            <th class="text-left py-3 px-2">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($content_seo as $content): ?>
                        <tr class="border-b border-gray-700 hover:bg-gray-700/50">
                            <td class="py-3 px-2">
                                <div>
                                    <div class="font-medium"><?php echo htmlspecialchars($content['title']); ?></div>
                                    <div class="text-xs text-gray-400"><?php echo htmlspecialchars($content['section']); ?></div>
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="font-mono text-xs bg-gray-700 px-2 py-1 rounded">
                                    <?php if (!empty($content['url_slug'])): ?>
                                        /<?php echo htmlspecialchars($content['url_slug']); ?>
                                    <?php else: ?>
                                        <span class="text-red-400">Sin URL</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="max-w-xs truncate">
                                    <?php echo htmlspecialchars($content['meta_title'] ?: 'Sin meta título'); ?>
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <?php
                                $score = $content['seo_score'] ?? 0;
                                $scoreClass = $score >= 80 ? 'seo-score-excellent' : ($score >= 60 ? 'seo-score-good' : 'seo-score-poor');
                                ?>
                                <div class="<?php echo $scoreClass; ?> font-bold">
                                    <?php echo $score; ?>/100
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <?php if ($content['is_published']): ?>
                                    <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">Publicado</span>
                                <?php else: ?>
                                    <span class="bg-gray-600 text-white px-2 py-1 rounded text-xs">Borrador</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-3 px-2">
                                <button onclick="editContentSEO(<?php echo $content['id']; ?>)"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs">
                                    <i class="ph ph-pencil mr-1"></i>Editar SEO
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- SEO Edit Modal -->
    <div id="seoModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">Editar SEO</h3>
                    <button onclick="closeSEOModal()" class="text-gray-400 hover:text-white">
                        <i class="ph ph-x text-xl"></i>
                    </button>
                </div>

                <form id="seoForm" method="POST" class="space-y-4">
                    <input type="hidden" name="content_id" id="modal_content_id">

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">URL Slug *</label>
                        <div class="flex gap-2">
                            <input type="text" name="url_slug" id="modal_url_slug" required
                                   class="flex-1 p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                   placeholder="mi-url-amigable">
                            <button type="button" onclick="generateSlugFromTitle()"
                                    class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                                <i class="ph ph-magic-wand"></i>
                            </button>
                        </div>
                        <div class="text-xs text-gray-400 mt-1">
                            Solo letras minúsculas, números y guiones. Ejemplo: mi-articulo-sobre-bitcoin
                        </div>
                        <div id="slug_preview" class="text-xs mt-1"></div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Meta Título</label>
                        <input type="text" name="meta_title" id="modal_meta_title"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="Título que aparecerá en Google">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Meta Descripción</label>
                        <textarea name="meta_description" id="modal_meta_description" rows="3"
                                  class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                  placeholder="Descripción que aparecerá en los resultados de búsqueda"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Palabras Clave</label>
                        <input type="text" name="meta_keywords" id="modal_meta_keywords"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                               placeholder="bitcoin, latinoamérica, criptomonedas">
                        <div class="text-xs text-gray-400 mt-1">Separar con comas</div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="is_published" id="modal_is_published" class="mr-2">
                        <label for="modal_is_published" class="text-sm text-gray-300">Publicado (visible en el sitio)</label>
                    </div>

                    <div class="flex justify-end gap-3 pt-4">
                        <button type="button" onclick="closeSEOModal()"
                                class="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded">
                            Cancelar
                        </button>
                        <button type="submit" name="update_content_seo"
                                class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-6 rounded">
                            <i class="ph ph-floppy-disk mr-2"></i>Guardar SEO
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Character counters for meta fields
        function addCharacterCounter(inputId, maxChars, recommendedMin, recommendedMax) {
            const input = document.querySelector(`[name="${inputId}"]`);
            if (!input) return;
            
            const counter = document.createElement('div');
            counter.className = 'text-xs mt-1';
            
            function updateCounter() {
                const length = input.value.length;
                let color = 'text-gray-400';
                let status = '';
                
                if (length === 0) {
                    color = 'text-gray-400';
                } else if (length < recommendedMin) {
                    color = 'text-yellow-400';
                    status = ' (muy corto)';
                } else if (length <= recommendedMax) {
                    color = 'text-green-400';
                    status = ' (óptimo)';
                } else if (length <= maxChars) {
                    color = 'text-orange-400';
                    status = ' (largo)';
                } else {
                    color = 'text-red-400';
                    status = ' (muy largo)';
                }
                
                counter.className = `text-xs mt-1 ${color}`;
                counter.textContent = `${length}/${maxChars} caracteres${status}`;
            }
            
            input.addEventListener('input', updateCounter);
            input.parentNode.appendChild(counter);
            updateCounter();
        }
        
        // Add counters to meta fields
        addCharacterCounter('default_meta_title', 70, 50, 60);
        addCharacterCounter('default_meta_description', 200, 150, 160);

        // Content data for modal
        const contentData = <?php echo json_encode($content_seo); ?>;

        // SEO Modal functions
        function editContentSEO(contentId) {
            const content = contentData.find(c => c.id == contentId);
            if (!content) return;

            // Populate modal fields
            document.getElementById('modal_content_id').value = content.id;
            document.getElementById('modal_url_slug').value = content.url_slug || '';
            document.getElementById('modal_meta_title').value = content.meta_title || '';
            document.getElementById('modal_meta_description').value = content.meta_description || '';
            document.getElementById('modal_meta_keywords').value = content.meta_keywords || '';
            document.getElementById('modal_is_published').checked = content.is_published == 1;

            // Update slug preview
            updateSlugPreview();

            // Show modal
            document.getElementById('seoModal').classList.remove('hidden');
        }

        function closeSEOModal() {
            document.getElementById('seoModal').classList.add('hidden');
        }

        function generateSlugFromTitle() {
            const contentId = document.getElementById('modal_content_id').value;
            const content = contentData.find(c => c.id == contentId);
            if (!content) return;

            // Generate slug from title
            const title = content.title;
            const slug = generateSlug(title);
            document.getElementById('modal_url_slug').value = slug;
            updateSlugPreview();
        }

        function generateSlug(text) {
            return text.toLowerCase()
                .replace(/[áàäâāã]/g, 'a')
                .replace(/[éèëêē]/g, 'e')
                .replace(/[íìïîī]/g, 'i')
                .replace(/[óòöôōõ]/g, 'o')
                .replace(/[úùüûū]/g, 'u')
                .replace(/ñ/g, 'n')
                .replace(/ç/g, 'c')
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/[\s-]+/g, '-')
                .replace(/^-+|-+$/g, '')
                .substring(0, 100);
        }

        function updateSlugPreview() {
            const slug = document.getElementById('modal_url_slug').value;
            const preview = document.getElementById('slug_preview');

            if (slug) {
                preview.innerHTML = `<span class="text-blue-400">Vista previa:</span> <span class="font-mono">https://abla.lat/${slug}</span>`;
            } else {
                preview.innerHTML = '';
            }
        }

        // Add event listeners
        document.getElementById('modal_url_slug').addEventListener('input', function() {
            // Auto-format slug as user types
            let value = this.value.toLowerCase()
                .replace(/[áàäâāã]/g, 'a')
                .replace(/[éèëêē]/g, 'e')
                .replace(/[íìïîī]/g, 'i')
                .replace(/[óòöôōõ]/g, 'o')
                .replace(/[úùüûū]/g, 'u')
                .replace(/ñ/g, 'n')
                .replace(/ç/g, 'c')
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/[\s-]+/g, '-')
                .replace(/^-+|-+$/g, '');

            this.value = value;
            updateSlugPreview();
        });

        // Add character counters to modal fields
        function addModalCharacterCounter(inputId, maxChars, recommendedMin, recommendedMax) {
            const input = document.getElementById(inputId);
            if (!input) return;

            const counter = document.createElement('div');
            counter.className = 'text-xs mt-1';

            function updateCounter() {
                const length = input.value.length;
                let color = 'text-gray-400';
                let status = '';

                if (length === 0) {
                    color = 'text-gray-400';
                } else if (length < recommendedMin) {
                    color = 'text-yellow-400';
                    status = ' (muy corto)';
                } else if (length <= recommendedMax) {
                    color = 'text-green-400';
                    status = ' (óptimo)';
                } else if (length <= maxChars) {
                    color = 'text-orange-400';
                    status = ' (largo)';
                } else {
                    color = 'text-red-400';
                    status = ' (muy largo)';
                }

                counter.className = `text-xs mt-1 ${color}`;
                counter.textContent = `${length}/${maxChars} caracteres${status}`;
            }

            input.addEventListener('input', updateCounter);
            input.parentNode.appendChild(counter);
            updateCounter();
        }

        // Add counters to modal fields
        addModalCharacterCounter('modal_meta_title', 70, 50, 60);
        addModalCharacterCounter('modal_meta_description', 200, 150, 160);

        // Close modal when clicking outside
        document.getElementById('seoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSEOModal();
            }
        });
    </script>
</body>
</html>
