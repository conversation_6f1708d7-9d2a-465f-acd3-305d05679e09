<?php
// Database schema update for SEO-friendly URLs
// Run this script to add SEO fields to existing tables

require_once '../config.php';
require_once '../includes/db.php';

$db = connectDB();

if (is_array($db) && isset($db['error'])) {
    die("Error de conexión a la base de datos: " . $db['error']);
}

$updates = [];
$errors = [];

// Add SEO fields to content table
try {
    // Check if columns already exist
    $result = $db->query("SHOW COLUMNS FROM content LIKE 'url_slug'");
    if ($result->num_rows == 0) {
        $sql = "ALTER TABLE content ADD COLUMN (
            url_slug VARCHAR(255) UNIQUE,
            meta_title VARCHAR(255),
            meta_description TEXT,
            meta_keywords VARCHAR(500),
            is_published BOOLEAN DEFAULT TRUE,
            publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            seo_score INT DEFAULT 0
        )";
        
        if ($db->query($sql)) {
            $updates[] = "✅ Campos SEO agregados a tabla 'content'";
        } else {
            $errors[] = "❌ Error al agregar campos SEO a 'content': " . $db->error;
        }
    } else {
        $updates[] = "ℹ️ Campos SEO ya existen en tabla 'content'";
    }
} catch (Exception $e) {
    $errors[] = "❌ Error procesando tabla 'content': " . $e->getMessage();
}

// Add SEO fields to initiatives table
try {
    $result = $db->query("SHOW COLUMNS FROM initiatives LIKE 'url_slug'");
    if ($result->num_rows == 0) {
        $sql = "ALTER TABLE initiatives ADD COLUMN (
            url_slug VARCHAR(255) UNIQUE,
            meta_title VARCHAR(255),
            meta_description TEXT,
            meta_keywords VARCHAR(500),
            is_published BOOLEAN DEFAULT TRUE,
            publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            seo_score INT DEFAULT 0
        )";
        
        if ($db->query($sql)) {
            $updates[] = "✅ Campos SEO agregados a tabla 'initiatives'";
        } else {
            $errors[] = "❌ Error al agregar campos SEO a 'initiatives': " . $db->error;
        }
    } else {
        $updates[] = "ℹ️ Campos SEO ya existen en tabla 'initiatives'";
    }
} catch (Exception $e) {
    $errors[] = "❌ Error procesando tabla 'initiatives': " . $e->getMessage();
}

// Add SEO fields to events table
try {
    $result = $db->query("SHOW COLUMNS FROM events LIKE 'url_slug'");
    if ($result->num_rows == 0) {
        $sql = "ALTER TABLE events ADD COLUMN (
            url_slug VARCHAR(255) UNIQUE,
            meta_title VARCHAR(255),
            meta_description TEXT,
            meta_keywords VARCHAR(500),
            is_published BOOLEAN DEFAULT TRUE,
            publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            seo_score INT DEFAULT 0
        )";
        
        if ($db->query($sql)) {
            $updates[] = "✅ Campos SEO agregados a tabla 'events'";
        } else {
            $errors[] = "❌ Error al agregar campos SEO a 'events': " . $db->error;
        }
    } else {
        $updates[] = "ℹ️ Campos SEO ya existen en tabla 'events'";
    }
} catch (Exception $e) {
    $errors[] = "❌ Error procesando tabla 'events': " . $e->getMessage();
}

// Create URL redirects table for managing redirects
try {
    $sql = "CREATE TABLE IF NOT EXISTS url_redirects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        old_url VARCHAR(500) NOT NULL,
        new_url VARCHAR(500) NOT NULL,
        redirect_type ENUM('301', '302', '307') DEFAULT '301',
        is_active BOOLEAN DEFAULT TRUE,
        hit_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_old_url (old_url),
        INDEX idx_active (is_active)
    )";
    
    if ($db->query($sql)) {
        $updates[] = "✅ Tabla 'url_redirects' creada";
    } else {
        $errors[] = "❌ Error al crear tabla 'url_redirects': " . $db->error;
    }
} catch (Exception $e) {
    $errors[] = "❌ Error creando tabla 'url_redirects': " . $e->getMessage();
}

// Create SEO settings table
try {
    $sql = "CREATE TABLE IF NOT EXISTS seo_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        setting_type ENUM('text', 'textarea', 'boolean', 'json') DEFAULT 'text',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($db->query($sql)) {
        $updates[] = "✅ Tabla 'seo_settings' creada";
        
        // Insert default SEO settings
        $default_settings = [
            ['default_meta_title', 'ABLA - Asociación Bitcoin Latinoamérica', 'text', 'Título meta por defecto'],
            ['default_meta_description', 'Promoviendo la adopción de Bitcoin en Latinoamérica a través de educación, desarrollo y colaboración entre comunidades.', 'textarea', 'Descripción meta por defecto'],
            ['default_meta_keywords', 'bitcoin, latinoamérica, criptomonedas, blockchain, educación, adopción', 'text', 'Palabras clave por defecto'],
            ['site_name', 'ABLA', 'text', 'Nombre del sitio'],
            ['twitter_handle', '@ABLALatam', 'text', 'Handle de Twitter'],
            ['facebook_page', '', 'text', 'Página de Facebook'],
            ['google_analytics_id', '', 'text', 'ID de Google Analytics'],
            ['google_search_console', '', 'text', 'Código de verificación de Google Search Console'],
            ['robots_txt_custom', '', 'textarea', 'Contenido personalizado para robots.txt'],
            ['sitemap_enabled', '1', 'boolean', 'Habilitar generación automática de sitemap']
        ];
        
        foreach ($default_settings as $setting) {
            $stmt = $db->prepare("INSERT IGNORE INTO seo_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $setting[0], $setting[1], $setting[2], $setting[3]);
            $stmt->execute();
            $stmt->close();
        }
        
        $updates[] = "✅ Configuraciones SEO por defecto insertadas";
    } else {
        $errors[] = "❌ Error al crear tabla 'seo_settings': " . $db->error;
    }
} catch (Exception $e) {
    $errors[] = "❌ Error creando tabla 'seo_settings': " . $e->getMessage();
}

// Generate automatic slugs for existing content
try {
    // Function to generate slug
    function generateSlug($text) {
        // Convert to lowercase
        $slug = strtolower($text);
        
        // Replace Spanish characters
        $slug = str_replace(['á', 'é', 'í', 'ó', 'ú', 'ñ', 'ü'], ['a', 'e', 'i', 'o', 'u', 'n', 'u'], $slug);
        
        // Remove special characters and replace with hyphens
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        
        return $slug;
    }
    
    // Generate slugs for content
    $result = $db->query("SELECT id, section, title FROM content WHERE url_slug IS NULL OR url_slug = ''");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $base_slug = generateSlug($row['title'] ?: $row['section']);
            $slug = $base_slug;
            $counter = 1;
            
            // Ensure uniqueness
            while (true) {
                $check = $db->prepare("SELECT id FROM content WHERE url_slug = ? AND id != ?");
                $check->bind_param("si", $slug, $row['id']);
                $check->execute();
                $check_result = $check->get_result();
                
                if ($check_result->num_rows == 0) {
                    break;
                }
                
                $slug = $base_slug . '-' . $counter;
                $counter++;
                $check->close();
            }
            
            // Update with generated slug
            $update = $db->prepare("UPDATE content SET url_slug = ? WHERE id = ?");
            $update->bind_param("si", $slug, $row['id']);
            $update->execute();
            $update->close();
        }
        $updates[] = "✅ Slugs automáticos generados para contenido existente";
    }
    
    // Generate slugs for initiatives
    $result = $db->query("SELECT id, name FROM initiatives WHERE url_slug IS NULL OR url_slug = ''");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $base_slug = generateSlug($row['name']);
            $slug = $base_slug;
            $counter = 1;
            
            // Ensure uniqueness
            while (true) {
                $check = $db->prepare("SELECT id FROM initiatives WHERE url_slug = ? AND id != ?");
                $check->bind_param("si", $slug, $row['id']);
                $check->execute();
                $check_result = $check->get_result();
                
                if ($check_result->num_rows == 0) {
                    break;
                }
                
                $slug = $base_slug . '-' . $counter;
                $counter++;
                $check->close();
            }
            
            // Update with generated slug
            $update = $db->prepare("UPDATE initiatives SET url_slug = ? WHERE id = ?");
            $update->bind_param("si", $slug, $row['id']);
            $update->execute();
            $update->close();
        }
        $updates[] = "✅ Slugs automáticos generados para iniciativas existentes";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Error generando slugs automáticos: " . $e->getMessage();
}

$db->close();

// Output results
echo "<!DOCTYPE html>\n<html lang='es'>\n<head>\n";
echo "<meta charset='UTF-8'>\n<title>Actualización SEO - ABLA</title>\n";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:50px auto;padding:20px;background:#f5f5f5}";
echo ".success{color:#28a745;margin:10px 0}.error{color:#dc3545;margin:10px 0}";
echo ".info{color:#17a2b8;margin:10px 0}h1{color:#333}</style>\n</head>\n<body>\n";

echo "<h1>🚀 Actualización de Schema SEO Completada</h1>\n";

if (!empty($updates)) {
    echo "<h2>✅ Actualizaciones Exitosas:</h2>\n";
    foreach ($updates as $update) {
        echo "<div class='success'>$update</div>\n";
    }
}

if (!empty($errors)) {
    echo "<h2>❌ Errores Encontrados:</h2>\n";
    foreach ($errors as $error) {
        echo "<div class='error'>$error</div>\n";
    }
}

echo "<div style='margin-top:30px;padding:20px;background:#e9ecef;border-radius:5px'>";
echo "<h3>📋 Próximos Pasos:</h3>";
echo "<ol>";
echo "<li>Revisar el panel de administración para las nuevas funciones SEO</li>";
echo "<li>Configurar meta títulos y descripciones personalizadas</li>";
echo "<li>Verificar que los URLs amigables funcionen correctamente</li>";
echo "<li>Configurar Google Analytics y Search Console</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align:center;margin-top:30px'>";
echo "<a href='/?route=admin' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px'>Ir al Panel de Administración</a>";
echo "</div>";

echo "\n</body>\n</html>";
?>
