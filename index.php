<?php
// Start session for admin authentication
session_start();

// Check if config file exists
if (!file_exists(__DIR__ . '/config.php')) {
    // Redirect to setup-config.php if it exists
    if (file_exists(__DIR__ . '/setup-config.php')) {
        header('Location: /setup-config.php');
        exit;
    } else {
        die('No se encontró el archivo de configuración. Por favor, crea un archivo config.php a partir de config-sample.php.');
    }
}

// Config exists, include it
require_once 'config.php';

// Include database and theme functions
require_once 'includes/db.php';
require_once 'includes/theme.php';
require_once 'includes/url_router.php';

// Test database connection
$db_connection = connectDB();

// Check if connection failed and setup is needed
$setup_needed = false;
if (is_array($db_connection) && isset($db_connection['error'])) {
    $setup_needed = true;
}

// Redirect to setup if needed
if ($setup_needed && (!isset($_GET['route']) || $_GET['route'] !== 'setup')) {
    header('Location: /setup/');
    exit;
}

// Handle setup route
if (isset($_GET['route']) && $_GET['route'] === 'setup') {
    include 'setup/index.php';
    exit;
}

// Simple router
$route = $_GET['route'] ?? 'home';

// Handle special SEO routes first
if ($route === 'sitemap.xml') {
    header('Content-Type: application/xml; charset=utf-8');
    echo generateSitemap();
    exit;
}

if ($route === 'robots.txt') {
    header('Content-Type: text/plain; charset=utf-8');
    echo generateRobotsTxt();
    exit;
}

// Try to handle content routing first
if (in_array($route, ['content', 'eventos', 'iniciativas']) || (isset($_GET['slug']) && !empty($_GET['slug']))) {
    if (handleContentRouting()) {
        exit; // Content was found and displayed
    }
}

// Include header
include 'includes/header.php';

// Route to appropriate page
switch($route) {
    case 'home':
        include 'pages/home.php';
        break;
    case 'descubre':
        include 'pages/descubre.php';
        break;
    case 'eventos':
        include 'pages/eventos.php';
        break;
    case 'nosotros':
        include 'pages/nosotros.php';
        break;
    case 'sumate':
        include 'pages/sumate.php';
        break;
    case 'admin':
        if(isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
            $admin_page = $_GET['page'] ?? 'dashboard';

            switch($admin_page) {
                case 'themes':
                    include 'admin/themes.php';
                    break;
                case 'db_init':
                    include 'admin/db_init.php';
                    break;
                case 'db_export':
                    include 'admin/db_export.php';
                    break;
                case 'inbox':
                    include 'admin/inbox.php';
                    break;
                case 'setup_manifesto_web':
                    include 'admin/setup_manifesto_web.php';
                    break;
                case 'seo':
                    include 'admin/seo.php';
                    break;
                case 'update_seo_schema':
                    include 'admin/update_seo_schema.php';
                    break;
                default:
                    include 'admin/dashboard.php';
            }
        } else {
            include 'admin/login.php';
        }
        break;
    case '404':
        // Set 404 header
        header("HTTP/1.0 404 Not Found");
        include 'pages/404.php';
        break;
    default:
        include 'pages/home.php';
}

// Include footer
include 'includes/footer.php';
?>