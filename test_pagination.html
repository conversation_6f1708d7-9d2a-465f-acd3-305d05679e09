<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Enhanced Initiatives Pagination</title>
    <link rel="stylesheet" href="/assets/css/styles.css">
    <link rel="stylesheet" href="https://unpkg.com/@phosphor-icons/web@2.0.3/src/regular/style.css">
    <style>
        body {
            background-color: var(--dark-bg);
            color: var(--light-text);
            font-family: 'Titillium Web', sans-serif;
            margin: 0;
            padding: 2rem;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        .feature-list {
            background-color: var(--card-bg);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 3rem;
            border: 1px solid var(--border-color);
        }
        .feature-list h3 {
            color: var(--primary);
            margin-bottom: 1rem;
        }
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✓";
            color: var(--primary);
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Enhanced Initiatives Pagination - Test Page</h1>
            <p class="text-lg text-gray-300">Testing the new pagination functionality and social media auto-detection</p>
        </div>

        <div class="feature-list">
            <h3>✨ Implemented Features</h3>
            <ul>
                <li><strong>Pagination Controls:</strong> Previous/next buttons and page numbers</li>
                <li><strong>Smooth Transitions:</strong> Fade-in animations between pages</li>
                <li><strong>Responsive Design:</strong> Mobile-friendly pagination controls</li>
                <li><strong>Page Indicators:</strong> Current page and total pages display</li>
                <li><strong>Performance Optimization:</strong> AJAX loading for large datasets</li>
                <li><strong>Social Media Auto-Detection:</strong> Automatic platform detection from URLs</li>
                <li><strong>Multi-Platform Support:</strong> Instagram, Twitter/X, Facebook, LinkedIn, TikTok, YouTube</li>
                <li><strong>Consistent Icon Styling:</strong> Platform-specific colors and icons</li>
                <li><strong>Accessibility:</strong> Proper alt text and keyboard navigation</li>
                <li><strong>Search Functionality:</strong> Real-time search with debouncing</li>
                <li><strong>URL State Management:</strong> Maintains page state on refresh</li>
                <li><strong>Loading Indicators:</strong> Visual feedback during transitions</li>
            </ul>
        </div>

        <div class="feature-list">
            <h3>🎯 Technical Implementation</h3>
            <ul>
                <li><strong>Database Enhancement:</strong> Added social media fields to initiatives table</li>
                <li><strong>API Endpoint:</strong> RESTful pagination API with search support</li>
                <li><strong>Social Media Detector:</strong> Automatic platform detection system</li>
                <li><strong>JavaScript Class:</strong> Modular pagination component</li>
                <li><strong>CSS Grid Layout:</strong> Responsive grid with smooth animations</li>
                <li><strong>Error Handling:</strong> Graceful fallbacks and error messages</li>
            </ul>
        </div>

        <!-- Enhanced Initiatives Section -->
        <section class="py-16 px-4" style="background-color: var(--card-bg); border-radius: 1rem;">
            <div class="container mx-auto max-w-6xl">
                <h2 class="text-3xl font-bold text-center mb-12">Iniciativas <span class="text-orange-500">Bitcoin</span> en Latinoamérica</h2>
                
                <!-- Enhanced Initiatives Container with Pagination -->
                <div id="initiatives-container">
                    <!-- Content will be loaded dynamically by JavaScript -->
                    <div class="initiatives-loading">
                        <div class="spinner"></div>
                        <span>Cargando iniciativas...</span>
                    </div>
                </div>
            </div>
        </section>

        <div class="feature-list">
            <h3>🔧 How to Test</h3>
            <ul>
                <li><strong>Pagination:</strong> Click on page numbers or use arrow keys</li>
                <li><strong>Search:</strong> Type in the search box to filter initiatives</li>
                <li><strong>Social Links:</strong> Hover over social media buttons to see platform-specific colors</li>
                <li><strong>Responsive:</strong> Resize the window to see mobile adaptations</li>
                <li><strong>Keyboard Navigation:</strong> Use left/right arrow keys to navigate pages</li>
                <li><strong>URL State:</strong> Refresh the page to see URL state persistence</li>
            </ul>
        </div>
    </div>

    <script src="/assets/js/initiatives-pagination.js"></script>
</body>
</html>
