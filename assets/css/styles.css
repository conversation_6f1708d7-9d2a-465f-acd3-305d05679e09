/* Custom styles for ABLA website */

:root {
    /* Primary colors */
    --primary: #F7931A;
    --primary-hover: #FF9500;
    --primary-light: #FEF4E8;

    /* Background colors */
    --dark-bg: #090C14;
    --card-bg: #13161F;
    --overlay-bg: #00000099;

    /* Text colors */
    --light-text: #FFFFFF;
    --muted-text: rgba(255, 255, 255, 0.7);

    /* Border colors */
    --border-color: rgba(255, 255, 255, 0.1);

    /* Status colors */
    --success: #22c55e;
    --error: #ef4444;
}

/* Typography enhancements */
body {
    font-family: 'Titillium Web', sans-serif;
    letter-spacing: -0.01em;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    letter-spacing: -0.02em;
}

.font-light {
    font-weight: 300;
}

.text-balance {
    text-wrap: balance;
}

/* Custom font sizes for hero section */
.text-10xl {
    font-size: 10rem;
    line-height: 0.9;
    letter-spacing: -0.04em;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.animate-fadeIn {
    animation: fadeIn 1s ease-in-out;
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-slideIn {
    animation: slideInFromBottom 0.5s ease-out;
}

/* Bitcoin-specific animations */
@keyframes bitcoinRotate {
    0% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(5deg);
    }
    75% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

.animate-bitcoin-rotate {
    animation: bitcoinRotate 6s ease-in-out infinite;
    transform-origin: center;
}

@keyframes bitcoinPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(247, 147, 26, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 15px rgba(247, 147, 26, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(247, 147, 26, 0);
    }
}

.animate-bitcoin-pulse {
    animation: bitcoinPulse 3s infinite;
}

@keyframes floatingBitcoin {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0px);
    }
}

.animate-floating {
    animation: floatingBitcoin 5s ease-in-out infinite;
}

@keyframes floatingX {
    0% {
        transform: translateX(0px);
    }
    50% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0px);
    }
}

.animate-floating-x {
    animation: floatingX 6s ease-in-out infinite;
}

@keyframes floatingXY {
    0% {
        transform: translate(0px, 0px);
    }
    25% {
        transform: translate(10px, -10px);
    }
    50% {
        transform: translate(0px, -15px);
    }
    75% {
        transform: translate(-10px, -5px);
    }
    100% {
        transform: translate(0px, 0px);
    }
}

.animate-floating-xy {
    animation: floatingXY 9s ease-in-out infinite;
}

@keyframes glowBitcoin {
    0% {
        filter: drop-shadow(0 0 2px rgba(247, 147, 26, 0.5));
    }
    50% {
        filter: drop-shadow(0 0 10px rgba(247, 147, 26, 0.8));
    }
    100% {
        filter: drop-shadow(0 0 2px rgba(247, 147, 26, 0.5));
    }
}

.animate-glow {
    animation: glowBitcoin 4s ease-in-out infinite;
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 5px rgba(247, 147, 26, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(247, 147, 26, 0.8), 0 0 30px rgba(247, 147, 26, 0.6);
    }
    100% {
        text-shadow: 0 0 5px rgba(247, 147, 26, 0.5);
    }
}

.animate-text-glow {
    animation: textGlow 4s ease-in-out infinite;
}

@keyframes spinBitcoin {
    from {
        transform: rotateY(0deg);
    }
    to {
        transform: rotateY(360deg);
    }
}

.animate-spin-slow {
    animation: spinBitcoin 15s linear infinite;
    transform-style: preserve-3d;
}

@keyframes spinBitcoinFast {
    from {
        transform: rotateY(0deg) rotateX(0deg);
    }
    to {
        transform: rotateY(360deg) rotateX(360deg);
    }
}

.animate-spin-fast {
    animation: spinBitcoinFast 5s linear infinite;
    transform-style: preserve-3d;
}

@keyframes orbitBitcoin {
    0% {
        transform: rotate(0deg) translateX(60px) rotate(0deg);
    }
    100% {
        transform: rotate(360deg) translateX(60px) rotate(-360deg);
    }
}

.animate-orbit {
    animation: orbitBitcoin 8s linear infinite;
    transform-origin: center;
}

@keyframes orbitReverse {
    0% {
        transform: rotate(0deg) translateX(60px) rotate(0deg);
    }
    100% {
        transform: rotate(-360deg) translateX(60px) rotate(360deg);
    }
}

.animate-orbit-reverse {
    animation: orbitReverse 10s linear infinite;
    transform-origin: center;
}

@keyframes lightning {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    10%, 90% {
        opacity: 0.3;
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

.animate-lightning {
    animation: lightning 2s ease-in-out infinite;
    animation-delay: calc(var(--delay, 0) * 1s);
}

/* Bitcoin particles animation */
.bitcoin-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: 0;
    pointer-events: none;
}

.bitcoin-particle {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: var(--primary);
    border-radius: 50%;
    opacity: 0.2;
    pointer-events: none;
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Bitcoin network animation */
.bitcoin-network {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;
}

.network-node {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: var(--primary);
    border-radius: 50%;
    opacity: 0.6;
}

.network-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, rgba(247, 147, 26, 0), rgba(247, 147, 26, 0.5), rgba(247, 147, 26, 0));
    transform-origin: left center;
    animation: networkPulse 3s infinite;
}

@keyframes networkPulse {
    0% {
        opacity: 0;
        transform: scaleX(0);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: scaleX(1);
    }
}

/* Bitcoin price ticker animation */
.price-ticker {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 2rem;
    border: 2px solid rgba(247, 147, 26, 0.5);
    font-family: 'Courier New', monospace;
    font-weight: bold;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.price-ticker:hover {
    border-color: rgba(247, 147, 26, 0.8);
    box-shadow: 0 6px 25px rgba(247, 147, 26, 0.2);
}

.price-value {
    position: relative;
}

@keyframes tickerChange {
    0%, 100% {
        color: var(--light-text);
    }
    50% {
        color: var(--primary);
    }
}

.ticker-up {
    animation: tickerChange 2s ease-out;
    color: #22c55e;
}

.ticker-down {
    animation: tickerChange 2s ease-out;
    color: #ef4444;
}

/* 3D Bitcoin coin effect */
.bitcoin-3d-container {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.bitcoin-3d {
    position: relative;
    transform-style: preserve-3d;
    animation: rotate3D 20s infinite linear;
}

@keyframes rotate3D {
    0% {
        transform: rotateY(0) rotateX(15deg);
    }
    100% {
        transform: rotateY(360deg) rotateX(15deg);
    }
}

.bitcoin-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    backface-visibility: hidden;
}

.bitcoin-face-front {
    background: linear-gradient(135deg, #f7931a, #ff9500);
    transform: translateZ(5px);
}

.bitcoin-face-back {
    background: linear-gradient(135deg, #ff9500, #f7931a);
    transform: rotateY(180deg) translateZ(5px);
}

.bitcoin-edge {
    position: absolute;
    width: 100%;
    height: 10px;
    background-color: #e68a19;
    transform: rotateX(90deg) translateZ(0);
    border-radius: 50%;
}

/* Custom styles for the popup */
.popup-overlay {
    background-color: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
}

/* Modern Form Elements */
input, textarea, select, .form-input {
    font-family: 'Titillium Web', sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    color: var(--light-text);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    width: 100%;
    font-size: 0.95rem;
}

input:hover,
textarea:hover,
select:hover,
.form-input:hover {
    border-color: rgba(247, 147, 26, 0.5);
}

input:focus,
textarea:focus,
select:focus,
.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.25);
    background-color: rgba(255, 255, 255, 0.07);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--light-text);
}

.form-hint {
    display: block;
    margin-top: 0.375rem;
    font-size: 0.875rem;
    color: var(--muted-text);
}

.form-error {
    display: block;
    margin-top: 0.375rem;
    font-size: 0.875rem;
    color: #ef4444;
}

/* Input with icon */
.input-icon-wrapper {
    position: relative;
}

.input-icon-wrapper .form-input {
    padding-left: 2.75rem;
}

.input-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: var(--muted-text);
    pointer-events: none;
}

/* Checkbox and Radio */
.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.form-checkbox input[type="checkbox"],
.form-radio input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
    accent-color: var(--primary);
}

/* Switch Toggle */
.form-switch {
    position: relative;
    display: inline-block;
    width: 3.5rem;
    height: 1.75rem;
}

.form-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    transition: .4s;
    border-radius: 1.75rem;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 1.25rem;
    width: 1.25rem;
    left: 0.25rem;
    bottom: 0.25rem;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: var(--primary);
}

input:focus + .switch-slider {
    box-shadow: 0 0 0 3px rgba(247, 147, 26, 0.25);
}

input:checked + .switch-slider:before {
    transform: translateX(1.75rem);
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.01em;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    text-decoration: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0);
    transition: background-color 0.3s ease;
}

.btn:hover::before {
    background-color: rgba(255, 255, 255, 0.1);
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(247, 147, 26, 0.2), 0 2px 4px -1px rgba(247, 147, 26, 0.1);
}

.btn-primary:hover {
    box-shadow: 0 6px 10px -1px rgba(247, 147, 26, 0.25), 0 4px 6px -1px rgba(247, 147, 26, 0.15);
    transform: translateY(-2px);
}

.btn-primary:active {
    box-shadow: 0 2px 4px -1px rgba(247, 147, 26, 0.2);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    color: var(--light-text);
    backdrop-filter: blur(5px);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background-color: rgba(247, 147, 26, 0.1);
    transform: translateY(-2px);
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2), 0 2px 4px -1px rgba(239, 68, 68, 0.1);
}

.btn-danger:hover {
    background-color: #dc2626;
    box-shadow: 0 6px 10px -1px rgba(239, 68, 68, 0.25), 0 4px 6px -1px rgba(239, 68, 68, 0.15);
    transform: translateY(-2px);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.875rem 1.75rem;
    font-size: 1.05rem;
}

/* Icon button */
.icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--light-text);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.icon-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0);
    transition: background-color 0.3s ease;
}

.icon-btn:hover::before {
    background-color: rgba(255, 255, 255, 0.1);
}

.icon-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.icon-btn:active {
    transform: translateY(1px);
}

.icon-btn-primary {
    background-color: var(--primary);
    color: white;
    border: none;
}

.icon-btn-sm {
    width: 2.25rem;
    height: 2.25rem;
    font-size: 0.875rem;
}

.icon-btn-lg {
    width: 3.25rem;
    height: 3.25rem;
    font-size: 1.25rem;
}

/* Modern Card Styles */
.card {
    background-color: var(--card-bg);
    border-radius: 1rem;
    padding: 1.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Initiative Description Formatting */
.initiative-description {
    line-height: 1.6;
}

.initiative-description strong,
.initiative-description b {
    font-weight: 700;
    color: var(--light-text);
}

.initiative-description em,
.initiative-description i {
    font-style: italic;
    color: var(--muted-text);
}

.initiative-description a {
    color: var(--primary);
    text-decoration: underline;
    transition: color 0.2s ease;
}

.initiative-description a:hover {
    color: var(--primary-hover);
    text-decoration: none;
}

/* Admin Formatting Toolbar */
.formatting-toolbar {
    background: #374151;
    border: 1px solid #4B5563;
    border-radius: 0.375rem 0.375rem 0 0;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
}

.formatting-toolbar button {
    background: #4B5563;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.formatting-toolbar button:hover {
    background: #6B7280;
}

.formatting-toolbar .divider {
    border-left: 1px solid #6B7280;
    margin: 0 0.5rem;
}

/* Admin Preview Area */
.description-preview {
    background: #374151;
    border: 1px solid #4B5563;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.description-preview-content {
    color: #E5E7EB;
    font-size: 0.875rem;
    line-height: 1.5;
}

.description-preview-content strong,
.description-preview-content b {
    font-weight: 700;
    color: #F9FAFB;
}

.description-preview-content em,
.description-preview-content i {
    font-style: italic;
    color: #D1D5DB;
}

.description-preview-content a {
    color: #F97316;
    text-decoration: underline;
}

.description-preview-content a:hover {
    color: #FB923C;
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-hover));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.card:hover::after {
    transform: scaleX(1);
}

.card-interactive {
    cursor: pointer;
}

.card-flat {
    box-shadow: none;
    border: 1px solid var(--border-color);
}

.card-flat:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-primary {
    border-top: 4px solid var(--primary);
}

.card-primary::after {
    display: none;
}

.card-header {
    margin: -1.75rem -1.75rem 1.5rem -1.75rem;
    padding: 1.25rem 1.75rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-footer {
    margin: 1.5rem -1.75rem -1.75rem -1.75rem;
    padding: 1.25rem 1.75rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Modern Navigation Styles */
.header-scrolled {
    background-color: rgba(19, 22, 31, 0.95);
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.2);
}

/* Modern Navbar */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    position: relative;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--light-text);
    text-decoration: none;
}

.navbar-brand img {
    height: 2.5rem;
    width: auto;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--muted-text);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.nav-link:hover {
    color: var(--light-text);
}

.nav-link:hover::after,
.nav-link.active::after {
    transform: scaleX(1);
    transform-origin: left;
}

.nav-link.active {
    color: var(--light-text);
    font-weight: 600;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    cursor: pointer;
}

.dropdown-toggle::after {
    content: '';
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-right: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    transform: rotate(45deg);
    transition: transform 0.2s ease;
}

.dropdown.show .dropdown-toggle::after {
    transform: rotate(-135deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 12rem;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    color: var(--muted-text);
    text-decoration: none;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--light-text);
}

.dropdown-divider {
    height: 1px;
    margin: 0.5rem 0;
    background-color: var(--border-color);
}

/* Mobile Menu Toggle */
.navbar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    padding: 0.5rem;
}

@media (max-width: 768px) {
    .navbar-toggle {
        display: block;
    }

    .navbar-collapse {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: var(--dark-bg);
        z-index: 1000;
        padding: 2rem;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .navbar-collapse.show {
        transform: translateX(0);
    }

    .navbar-nav {
        flex-direction: column;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        border: none;
        background-color: transparent;
        padding-left: 1rem;
        margin-top: 0.5rem;
        opacity: 1;
        visibility: visible;
        transform: none;
    }

    .navbar-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        color: var(--light-text);
        cursor: pointer;
        padding: 0.5rem;
    }
}

/* Custom styles for the footer */
.footer-link {
    color: var(--muted-text);
    transition: color 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.footer-link:hover {
    color: var(--light-text);
}

/* Custom styles for social media icons */
.social-icon {
    color: var(--muted-text);
    transition: all 0.2s ease;
}

.social-icon:hover {
    color: var(--light-text);
    transform: scale(1.1) translateY(-1px);
}

/* Modern Badge Component */
.badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    line-height: 1;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.badge-icon {
    font-size: 0.875rem;
}

.badge-primary {
    background-color: rgba(247, 147, 26, 0.15);
    color: var(--primary);
}

.badge-primary-solid {
    background-color: var(--primary);
    color: white;
}

.badge-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--muted-text);
}

.badge-light {
    background-color: var(--primary-light);
    color: var(--primary);
}

.badge-success {
    background-color: rgba(34, 197, 94, 0.15);
    color: var(--success);
}

.badge-error {
    background-color: rgba(239, 68, 68, 0.15);
    color: var(--error);
}

.badge-outline {
    background-color: transparent;
    border: 1px solid currentColor;
}

.badge-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.675rem;
}

.badge-lg {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.badge-pill {
    border-radius: 9999px;
}

.badge-rounded {
    border-radius: 0.375rem;
}

/* Interactive badge */
.badge-interactive {
    cursor: pointer;
}

.badge-interactive:hover {
    filter: brightness(1.1);
    transform: translateY(-1px);
}

.badge-dismissible {
    padding-right: 0.375rem;
}

.badge-dismiss-icon {
    margin-left: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
}

.badge-dismiss-icon:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .text-10xl {
        font-size: 6rem;
    }
}

/* Modern Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    margin-bottom: 1.5rem;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.95rem;
}

.table th {
    background-color: rgba(255, 255, 255, 0.03);
    padding: 1rem 1.25rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--muted-text);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.table th:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, var(--primary), transparent);
    opacity: 0.3;
}

.table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--light-text);
    vertical-align: middle;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover td {
    background-color: rgba(255, 255, 255, 0.03);
}

.table-striped tr:nth-child(even) td {
    background-color: rgba(255, 255, 255, 0.01);
}

.table-striped tr:hover td {
    background-color: rgba(255, 255, 255, 0.03);
}

.table-compact th,
.table-compact td {
    padding: 0.625rem 1rem;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: flex-end;
}

.table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--muted-text);
}

.table-empty-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-empty-text {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.table-empty-subtext {
    font-size: 0.875rem;
    opacity: 0.7;
}

.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    border-top: 1px solid var(--border-color);
    background-color: rgba(255, 255, 255, 0.01);
}

.pagination-info {
    color: var(--muted-text);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--muted-text);
    transition: all 0.2s ease;
    cursor: pointer;
}

.pagination-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-text);
}

.pagination-button.active {
    background-color: var(--primary);
    color: white;
}

.pagination-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Enhanced Initiatives Pagination */
.initiatives-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
    padding: 1rem;
}

.initiatives-pagination .pagination-button {
    min-width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-color);
}

.initiatives-pagination .pagination-button:hover:not(.disabled) {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(247, 147, 26, 0.3);
}

.initiatives-pagination .pagination-button.active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(247, 147, 26, 0.4);
}

.initiatives-pagination .pagination-info {
    margin: 0 1rem;
    font-size: 0.875rem;
    color: var(--muted-text);
    white-space: nowrap;
}

/* Initiatives Grid */
.initiatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    min-height: 400px;
}

@media (max-width: 768px) {
    .initiatives-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        min-height: 300px;
    }

    .initiatives-pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .initiatives-pagination .pagination-button {
        min-width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .initiatives-pagination .pagination-info {
        width: 100%;
        text-align: center;
        margin: 0.5rem 0;
    }
}

/* Loading States */
.initiatives-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: var(--muted-text);
}

.initiatives-loading .spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Social Media Links Enhancement */
.social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid var(--border-color);
    background-color: rgba(255, 255, 255, 0.05);
}

.social-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border-color: var(--primary);
}

.social-link[data-platform="instagram"]:hover {
    background-color: #E4405F;
    border-color: #E4405F;
    color: white;
}

.social-link[data-platform="twitter"]:hover {
    background-color: #000000;
    border-color: #000000;
    color: white;
}

.social-link[data-platform="facebook"]:hover {
    background-color: #1877F2;
    border-color: #1877F2;
    color: white;
}

.social-link[data-platform="linkedin"]:hover {
    background-color: #0A66C2;
    border-color: #0A66C2;
    color: white;
}

.social-link[data-platform="youtube"]:hover {
    background-color: #FF0000;
    border-color: #FF0000;
    color: white;
}

.social-link[data-platform="tiktok"]:hover {
    background-color: #000000;
    border-color: #000000;
    color: white;
}

/* Admin Dashboard Styles */
.admin-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.admin-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.25rem;
}

.admin-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--light-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-card-icon {
    color: var(--primary);
}

.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    background-color: rgba(247, 147, 26, 0.1);
    color: var(--primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--light-text);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--muted-text);
    font-size: 0.875rem;
}

.stat-change {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* Custom styles for the QR code */
.qr-code-container {
    background-color: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    width: 8rem;
    height: 8rem;
    margin: 0 auto;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Custom styles for code blocks */
.code-block {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--light-text);
    overflow-x: auto;
}

/* Modern Alerts and Notifications */
.alert {
    border-radius: 0.75rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-icon {
    flex-shrink: 0;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.alert-content {
    flex-grow: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    color: inherit;
    opacity: 0.9;
}

.alert-close {
    flex-shrink: 0;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.alert-close:hover {
    opacity: 1;
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    color: var(--success);
}

.alert-success::before {
    background-color: var(--success);
}

.alert-success .alert-icon {
    background-color: rgba(34, 197, 94, 0.2);
    color: var(--success);
}

.alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: var(--error);
}

.alert-error::before {
    background-color: var(--error);
}

.alert-error .alert-icon {
    background-color: rgba(239, 68, 68, 0.2);
    color: var(--error);
}

.alert-info {
    background-color: rgba(247, 147, 26, 0.1);
    border: 1px solid rgba(247, 147, 26, 0.2);
    color: var(--primary);
}

.alert-info::before {
    background-color: var(--primary);
}

.alert-info .alert-icon {
    background-color: rgba(247, 147, 26, 0.2);
    color: var(--primary);
}

.alert-light {
    background-color: var(--primary-light);
    border: 1px solid rgba(247, 147, 26, 0.2);
    color: var(--primary);
}

.alert-light::before {
    background-color: var(--primary);
}

.alert-light .alert-icon {
    background-color: rgba(247, 147, 26, 0.2);
    color: var(--primary);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 24rem;
}

.toast {
    border-radius: 0.75rem;
    padding: 1rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideInRight 0.3s ease forwards;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-icon {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.toast-content {
    flex-grow: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--light-text);
}

.toast-message {
    color: var(--muted-text);
    font-size: 0.875rem;
}

.toast-close {
    flex-shrink: 0;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.2s;
    color: var(--muted-text);
}

.toast-close:hover {
    opacity: 1;
}

.toast-success .toast-icon {
    background-color: rgba(34, 197, 94, 0.2);
    color: var(--success);
}

.toast-error .toast-icon {
    background-color: rgba(239, 68, 68, 0.2);
    color: var(--error);
}

.toast-info .toast-icon {
    background-color: rgba(247, 147, 26, 0.2);
    color: var(--primary);
}

/* Divider with text */
.divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color);
}

.divider::before {
    margin-right: 0.75rem;
}

.divider::after {
    margin-left: 0.75rem;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    background-color: var(--card-bg);
    color: var(--light-text);
    text-align: center;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    font-size: 0.875rem;
    white-space: nowrap;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Alert Styles */
.alert {
    display: flex;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.alert-error {
    background-color: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.alert-warning {
    background-color: rgba(247, 147, 26, 0.2);
    border: 1px solid rgba(247, 147, 26, 0.3);
    color: #f7931a;
}

.alert-icon {
    display: flex;
    align-items: flex-start;
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    color: var(--light-text);
    font-size: 0.95rem;
}
