/**
 * Enhanced Initiatives Pagination with Social Media Auto-Detection
 * Provides smooth pagination, loading states, and responsive design
 */

class InitiativesPagination {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            perPage: 6,
            apiUrl: '/api/initiatives.php',
            enableSearch: true,
            enableKeyboardNav: true,
            ...options
        };
        
        this.currentPage = 1;
        this.totalPages = 1;
        this.isLoading = false;
        this.searchTimeout = null;
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('Initiatives container not found');
            return;
        }
        
        this.createHTML();
        this.bindEvents();
        this.loadPage(1);
        
        // Restore page from URL hash
        const urlPage = this.getPageFromURL();
        if (urlPage > 1) {
            this.loadPage(urlPage);
        }
    }
    
    createHTML() {
        this.container.innerHTML = `
            ${this.options.enableSearch ? this.createSearchHTML() : ''}
            <div class="initiatives-grid" id="initiatives-grid"></div>
            <div class="initiatives-pagination" id="initiatives-pagination"></div>
        `;
        
        this.gridContainer = this.container.querySelector('#initiatives-grid');
        this.paginationContainer = this.container.querySelector('#initiatives-pagination');
        this.searchInput = this.container.querySelector('#initiatives-search');
    }
    
    createSearchHTML() {
        return `
            <div class="mb-6">
                <div class="relative max-w-md mx-auto">
                    <input 
                        type="text" 
                        id="initiatives-search"
                        placeholder="Buscar iniciativas..."
                        class="w-full px-4 py-2 pl-10 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-orange-500"
                    >
                    <i class="ph ph-magnifying-glass absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.search(e.target.value);
                }, 300);
            });
        }
        
        // Keyboard navigation
        if (this.options.enableKeyboardNav) {
            document.addEventListener('keydown', (e) => {
                if (e.target.tagName === 'INPUT') return;
                
                if (e.key === 'ArrowLeft' && this.currentPage > 1) {
                    e.preventDefault();
                    this.loadPage(this.currentPage - 1);
                } else if (e.key === 'ArrowRight' && this.currentPage < this.totalPages) {
                    e.preventDefault();
                    this.loadPage(this.currentPage + 1);
                }
            });
        }
        
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            const page = this.getPageFromURL();
            this.loadPage(page, false);
        });
    }
    
    async loadPage(page, updateURL = true) {
        if (this.isLoading || page < 1) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const searchQuery = this.searchInput ? this.searchInput.value : '';
            const url = new URL(this.options.apiUrl, window.location.origin);
            url.searchParams.set('page', page);
            url.searchParams.set('per_page', this.options.perPage);
            if (searchQuery) {
                url.searchParams.set('search', searchQuery);
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.currentPage = data.data.pagination.current_page;
                this.totalPages = data.data.pagination.total_pages;
                
                this.renderInitiatives(data.data.initiatives);
                this.renderPagination(data.data.pagination);
                
                if (updateURL) {
                    this.updateURL(page);
                }
                
                // Scroll to top of initiatives section
                this.container.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                this.showError(data.error || 'Error loading initiatives');
            }
        } catch (error) {
            console.error('Error loading initiatives:', error);
            this.showError('Network error. Please try again.');
        } finally {
            this.isLoading = false;
        }
    }
    
    search(query) {
        this.currentPage = 1;
        this.loadPage(1);
    }
    
    showLoading() {
        this.gridContainer.innerHTML = `
            <div class="initiatives-loading">
                <div class="spinner"></div>
                <span>Cargando iniciativas...</span>
            </div>
        `;
        this.paginationContainer.innerHTML = '';
    }
    
    showError(message) {
        this.gridContainer.innerHTML = `
            <div class="initiatives-loading">
                <i class="ph ph-warning-circle text-red-500 text-2xl mr-2"></i>
                <span>${message}</span>
            </div>
        `;
    }
    
    renderInitiatives(initiatives) {
        if (initiatives.length === 0) {
            this.gridContainer.innerHTML = `
                <div class="initiatives-loading">
                    <i class="ph ph-magnifying-glass text-gray-400 text-2xl mr-2"></i>
                    <span>No se encontraron iniciativas</span>
                </div>
            `;
            return;
        }
        
        this.gridContainer.innerHTML = initiatives.map(initiative => this.renderInitiativeCard(initiative)).join('');
        
        // Add fade-in animation
        this.gridContainer.querySelectorAll('.card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    renderInitiativeCard(initiative) {
        const socialLinks = initiative.social_links.map(link => `
            <a href="${link.url}" target="_blank" rel="noopener noreferrer"
               class="social-link" data-platform="${link.platform}" title="${link.name}">
                <i class="ph ${link.icon}"></i> ${link.display_text}
            </a>
        `).join('');

        // Add avatar source indicator for real social media profile pictures
        const avatarSourceBadge = this.getAvatarSourceBadge(initiative.avatar_source);

        return `
            <div class="card">
                <div class="flex items-center mb-4">
                    <div class="relative">
                        <img src="${initiative.avatar_url}"
                             alt="${initiative.name}"
                             class="w-12 h-12 rounded-full object-cover border-2 border-orange-500/30"
                             loading="lazy"
                             onerror="this.src='${this.getDefaultAvatar(initiative.name)}'">
                        ${avatarSourceBadge}
                    </div>
                    <div class="ml-4">
                        <h3 class="text-xl font-bold text-white">${initiative.name}</h3>
                        ${this.renderSocialUsernames(initiative)}
                    </div>
                </div>

                <div class="text-gray-300 mb-6 initiative-description">
                    ${initiative.description}
                </div>

                <div class="social-links">
                    ${socialLinks}
                </div>
            </div>
        `;
    }

    getAvatarSourceBadge(source) {
        if (!source || source === 'fallback') return '';

        const badges = {
            'instagram': '<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-pink-500 rounded-full flex items-center justify-center" title="Instagram Profile Picture"><i class="ph ph-instagram-logo text-white text-xs"></i></div>',
            'twitter': '<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-black rounded-full flex items-center justify-center" title="Twitter Profile Picture"><i class="ph ph-x-logo text-white text-xs"></i></div>',
            'manual': '<div class="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center" title="Manual Upload"><i class="ph ph-user text-white text-xs"></i></div>'
        };

        return badges[source] || '';
    }

    renderSocialUsernames(initiative) {
        const usernames = [];

        if (initiative.instagram_user) {
            const username = initiative.instagram_user.startsWith('@') ? initiative.instagram_user : '@' + initiative.instagram_user;
            usernames.push(`<span class="text-pink-500 text-sm flex items-center gap-1"><i class="ph ph-instagram-logo"></i>${username}</span>`);
        }

        if (initiative.twitter_user) {
            const username = initiative.twitter_user.startsWith('@') ? initiative.twitter_user : '@' + initiative.twitter_user;
            usernames.push(`<span class="text-gray-400 text-sm flex items-center gap-1"><i class="ph ph-x-logo"></i>${username}</span>`);
        }

        return usernames.length > 0 ? `<div class="flex flex-wrap gap-2 mt-1">${usernames.join('')}</div>` : '';
    }

    getDefaultAvatar(name) {
        // Generate a default avatar URL
        const initials = name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
        return `data:image/svg+xml;base64,${btoa(`
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="24" cy="24" r="24" fill="#F7931A"/>
                <text x="24" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">${initials}</text>
            </svg>
        `)}`;
    }
    
    renderPagination(pagination) {
        if (pagination.total_pages <= 1) {
            this.paginationContainer.innerHTML = '';
            return;
        }
        
        const buttons = [];
        
        // Previous button
        buttons.push(`
            <button class="pagination-button ${!pagination.has_prev ? 'disabled' : ''}" 
                    data-page="${pagination.prev_page}" 
                    ${!pagination.has_prev ? 'disabled' : ''}>
                <i class="ph ph-caret-left"></i>
            </button>
        `);
        
        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        if (startPage > 1) {
            buttons.push(`<button class="pagination-button" data-page="1">1</button>`);
            if (startPage > 2) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="pagination-button ${i === pagination.current_page ? 'active' : ''}" 
                        data-page="${i}">${i}</button>
            `);
        }
        
        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
            buttons.push(`<button class="pagination-button" data-page="${pagination.total_pages}">${pagination.total_pages}</button>`);
        }
        
        // Next button
        buttons.push(`
            <button class="pagination-button ${!pagination.has_next ? 'disabled' : ''}" 
                    data-page="${pagination.next_page}" 
                    ${!pagination.has_next ? 'disabled' : ''}>
                <i class="ph ph-caret-right"></i>
            </button>
        `);
        
        // Pagination info
        const info = `
            <div class="pagination-info">
                Página ${pagination.current_page} de ${pagination.total_pages} 
                (${pagination.total_items} iniciativas)
            </div>
        `;
        
        this.paginationContainer.innerHTML = buttons.join('') + info;
        
        // Bind click events
        this.paginationContainer.querySelectorAll('.pagination-button:not(.disabled)').forEach(button => {
            button.addEventListener('click', () => {
                const page = parseInt(button.dataset.page);
                if (page && page !== this.currentPage) {
                    this.loadPage(page);
                }
            });
        });
    }
    
    getPageFromURL() {
        const hash = window.location.hash;
        const match = hash.match(/#page-(\d+)/);
        return match ? parseInt(match[1]) : 1;
    }
    
    updateURL(page) {
        const newHash = page > 1 ? `#page-${page}` : '';
        if (window.location.hash !== newHash) {
            history.pushState(null, '', window.location.pathname + newHash);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const initiativesContainer = document.getElementById('initiatives-container');
    if (initiativesContainer) {
        new InitiativesPagination('initiatives-container', {
            perPage: 6,
            enableSearch: true,
            enableKeyboardNav: true
        });
    }
});
