<?php
// SEO utility functions for ABLA website

/**
 * Generate SEO-friendly URL slug from text
 */
function generateSlug($text, $maxLength = 100) {
    if (empty($text)) {
        return '';
    }
    
    // Convert to lowercase
    $slug = strtolower(trim($text));
    
    // Replace Spanish characters
    $replacements = [
        'á' => 'a', 'à' => 'a', 'ä' => 'a', 'â' => 'a', 'ā' => 'a', 'ã' => 'a',
        'é' => 'e', 'è' => 'e', 'ë' => 'e', 'ê' => 'e', 'ē' => 'e',
        'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'î' => 'i', 'ī' => 'i',
        'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'ô' => 'o', 'ō' => 'o', 'õ' => 'o',
        'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'û' => 'u', 'ū' => 'u',
        'ñ' => 'n', 'ç' => 'c'
    ];
    
    $slug = strtr($slug, $replacements);
    
    // Remove special characters and replace with hyphens
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    
    // Limit length
    if (strlen($slug) > $maxLength) {
        $slug = substr($slug, 0, $maxLength);
        $slug = rtrim($slug, '-');
    }
    
    return $slug;
}

/**
 * Validate URL slug format
 */
function validateSlug($slug) {
    $errors = [];
    
    if (empty($slug)) {
        $errors[] = 'El slug no puede estar vacío';
        return $errors;
    }
    
    if (strlen($slug) < 3) {
        $errors[] = 'El slug debe tener al menos 3 caracteres';
    }
    
    if (strlen($slug) > 100) {
        $errors[] = 'El slug no puede tener más de 100 caracteres';
    }
    
    if (!preg_match('/^[a-z0-9-]+$/', $slug)) {
        $errors[] = 'El slug solo puede contener letras minúsculas, números y guiones';
    }
    
    if (preg_match('/^-|-$/', $slug)) {
        $errors[] = 'El slug no puede empezar o terminar con guión';
    }
    
    if (preg_match('/--/', $slug)) {
        $errors[] = 'El slug no puede contener guiones consecutivos';
    }
    
    // Reserved words
    $reserved = ['admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'home', 'index', 'search', 'login', 'register', 'dashboard'];
    if (in_array($slug, $reserved)) {
        $errors[] = 'El slug "' . $slug . '" está reservado y no se puede usar';
    }
    
    return $errors;
}

/**
 * Check if slug is unique in database
 */
function isSlugUnique($slug, $table, $excludeId = null) {
    $db = connectDB();
    if (is_array($db)) return false;
    
    $sql = "SELECT id FROM $table WHERE url_slug = ?";
    $params = [$slug];
    $types = "s";
    
    if ($excludeId) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
        $types .= "i";
    }
    
    $stmt = $db->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $isUnique = $result->num_rows === 0;
    
    $stmt->close();
    $db->close();
    
    return $isUnique;
}

/**
 * Generate unique slug for a table
 */
function generateUniqueSlug($text, $table, $excludeId = null) {
    $baseSlug = generateSlug($text);
    $slug = $baseSlug;
    $counter = 1;
    
    while (!isSlugUnique($slug, $table, $excludeId)) {
        $slug = $baseSlug . '-' . $counter;
        $counter++;
        
        // Prevent infinite loop
        if ($counter > 1000) {
            $slug = $baseSlug . '-' . time();
            break;
        }
    }
    
    return $slug;
}

/**
 * Get SEO settings from database
 */
function getSEOSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        $db = connectDB();
        
        if (!is_array($db)) {
            $result = $db->query("SELECT setting_key, setting_value FROM seo_settings");
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            }
            $db->close();
        }
    }
    
    return $settings;
}

/**
 * Get SEO setting value
 */
function getSEOSetting($key, $default = '') {
    $settings = getSEOSettings();
    return $settings[$key] ?? $default;
}

/**
 * Calculate SEO score for content
 */
function calculateSEOScore($data) {
    $score = 0;
    $maxScore = 100;
    
    // Title (20 points)
    if (!empty($data['title'])) {
        $titleLength = strlen($data['title']);
        if ($titleLength >= 30 && $titleLength <= 60) {
            $score += 20;
        } elseif ($titleLength >= 20 && $titleLength <= 70) {
            $score += 15;
        } elseif (!empty($data['title'])) {
            $score += 10;
        }
    }
    
    // Meta description (20 points)
    if (!empty($data['meta_description'])) {
        $descLength = strlen($data['meta_description']);
        if ($descLength >= 120 && $descLength <= 160) {
            $score += 20;
        } elseif ($descLength >= 100 && $descLength <= 180) {
            $score += 15;
        } elseif (!empty($data['meta_description'])) {
            $score += 10;
        }
    }
    
    // URL slug (15 points)
    if (!empty($data['url_slug'])) {
        $slugLength = strlen($data['url_slug']);
        if ($slugLength >= 10 && $slugLength <= 50) {
            $score += 15;
        } elseif ($slugLength >= 5 && $slugLength <= 70) {
            $score += 10;
        } elseif (!empty($data['url_slug'])) {
            $score += 5;
        }
    }
    
    // Content length (15 points)
    if (!empty($data['content'])) {
        $contentLength = strlen(strip_tags($data['content']));
        if ($contentLength >= 300) {
            $score += 15;
        } elseif ($contentLength >= 150) {
            $score += 10;
        } elseif ($contentLength >= 50) {
            $score += 5;
        }
    }
    
    // Keywords (10 points)
    if (!empty($data['meta_keywords'])) {
        $keywords = explode(',', $data['meta_keywords']);
        $keywordCount = count(array_filter(array_map('trim', $keywords)));
        if ($keywordCount >= 3 && $keywordCount <= 10) {
            $score += 10;
        } elseif ($keywordCount >= 1 && $keywordCount <= 15) {
            $score += 5;
        }
    }
    
    // Image alt text (10 points) - if image_url exists
    if (!empty($data['image_url'])) {
        $score += 5; // Basic points for having an image
        // Additional points could be added for alt text if implemented
    }
    
    // Published status (10 points)
    if (isset($data['is_published']) && $data['is_published']) {
        $score += 10;
    }
    
    return min($score, $maxScore);
}

/**
 * Generate meta tags for a page
 */
function generateMetaTags($data) {
    $settings = getSEOSettings();
    
    $title = $data['meta_title'] ?? $data['title'] ?? getSEOSetting('default_meta_title');
    $description = $data['meta_description'] ?? getSEOSetting('default_meta_description');
    $keywords = $data['meta_keywords'] ?? getSEOSetting('default_meta_keywords');
    $image = $data['image_url'] ?? '';
    $url = getCurrentURL();
    
    $tags = [];
    
    // Basic meta tags
    $tags[] = '<title>' . htmlspecialchars($title) . '</title>';
    $tags[] = '<meta name="description" content="' . htmlspecialchars($description) . '">';
    if ($keywords) {
        $tags[] = '<meta name="keywords" content="' . htmlspecialchars($keywords) . '">';
    }
    
    // Open Graph tags
    $tags[] = '<meta property="og:title" content="' . htmlspecialchars($title) . '">';
    $tags[] = '<meta property="og:description" content="' . htmlspecialchars($description) . '">';
    $tags[] = '<meta property="og:url" content="' . htmlspecialchars($url) . '">';
    $tags[] = '<meta property="og:type" content="website">';
    $tags[] = '<meta property="og:site_name" content="' . htmlspecialchars(getSEOSetting('site_name', 'ABLA')) . '">';
    
    if ($image) {
        $tags[] = '<meta property="og:image" content="' . htmlspecialchars($image) . '">';
    }
    
    // Twitter Card tags
    $tags[] = '<meta name="twitter:card" content="summary_large_image">';
    $tags[] = '<meta name="twitter:title" content="' . htmlspecialchars($title) . '">';
    $tags[] = '<meta name="twitter:description" content="' . htmlspecialchars($description) . '">';
    
    $twitterHandle = getSEOSetting('twitter_handle');
    if ($twitterHandle) {
        $tags[] = '<meta name="twitter:site" content="' . htmlspecialchars($twitterHandle) . '">';
    }
    
    if ($image) {
        $tags[] = '<meta name="twitter:image" content="' . htmlspecialchars($image) . '">';
    }
    
    // Canonical URL
    $tags[] = '<link rel="canonical" href="' . htmlspecialchars($url) . '">';
    
    return implode("\n    ", $tags);
}

/**
 * Get current URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'abla.lat';
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    
    return $protocol . '://' . $host . $uri;
}

/**
 * Generate breadcrumb JSON-LD
 */
function generateBreadcrumbSchema($breadcrumbs) {
    $items = [];
    $position = 1;
    
    foreach ($breadcrumbs as $crumb) {
        $items[] = [
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $crumb['name'],
            'item' => $crumb['url']
        ];
        $position++;
    }
    
    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'BreadcrumbList',
        'itemListElement' => $items
    ];
    
    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>';
}
?>
