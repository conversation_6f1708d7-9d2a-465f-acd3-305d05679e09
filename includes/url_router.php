<?php
// URL Router for SEO-friendly URLs
// Handles slug-based routing and redirects

require_once 'seo.php';

/**
 * Resolve URL slug to content
 */
function resolveSlugToContent($slug, $type = 'content') {
    $db = connectDB();
    if (is_array($db)) return null;
    
    $table = $type;
    $stmt = $db->prepare("SELECT * FROM $table WHERE url_slug = ? AND is_published = 1 LIMIT 1");
    $stmt->bind_param("s", $slug);
    $stmt->execute();
    $result = $stmt->get_result();
    $content = $result->fetch_assoc();
    
    $stmt->close();
    $db->close();
    
    return $content;
}

/**
 * Resolve legacy ID to slug and redirect
 */
function resolveLegacyIdToSlug($id, $type = 'content') {
    $db = connectDB();
    if (is_array($db)) return null;
    
    $table = $type;
    $stmt = $db->prepare("SELECT url_slug FROM $table WHERE id = ? LIMIT 1");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    $stmt->close();
    $db->close();
    
    return $row ? $row['url_slug'] : null;
}

/**
 * Handle content routing based on slug
 */
function handleContentRouting() {
    $route = $_GET['route'] ?? '';
    $slug = $_GET['slug'] ?? '';
    $legacy_id = $_GET['legacy_id'] ?? '';
    
    // Handle legacy ID redirects
    if (!empty($legacy_id)) {
        $new_slug = resolveLegacyIdToSlug($legacy_id, $route);
        if ($new_slug) {
            $redirect_url = "/$new_slug";
            header("Location: $redirect_url", true, 301);
            exit;
        } else {
            // Legacy content not found, show 404
            include 'pages/404.php';
            exit;
        }
    }
    
    // Handle slug-based routing
    if (!empty($slug)) {
        switch ($route) {
            case 'content':
                // Generic content routing
                $content = resolveSlugToContent($slug, 'content');
                if ($content) {
                    // Set content data for the page
                    $GLOBALS['seo_content'] = $content;
                    
                    // Route to appropriate page based on section
                    switch ($content['section']) {
                        case 'manifesto':
                        case 'why_abla':
                        case 'statutes':
                        case 'why_founder':
                        case 'allies':
                            include 'pages/nosotros.php';
                            break;
                        case 'about':
                            include 'pages/nosotros.php';
                            break;
                        case 'hero':
                            include 'pages/home.php';
                            break;
                        default:
                            include 'pages/content.php';
                            break;
                    }
                } else {
                    include 'pages/404.php';
                }
                break;
                
            case 'eventos':
                $event = resolveSlugToContent($slug, 'events');
                if ($event) {
                    $GLOBALS['seo_content'] = $event;
                    include 'pages/evento.php';
                } else {
                    include 'pages/404.php';
                }
                break;
                
            case 'iniciativas':
                $initiative = resolveSlugToContent($slug, 'initiatives');
                if ($initiative) {
                    $GLOBALS['seo_content'] = $initiative;
                    include 'pages/iniciativa.php';
                } else {
                    include 'pages/404.php';
                }
                break;
                
            default:
                include 'pages/404.php';
                break;
        }
        exit;
    }
    
    return false; // No routing handled
}

/**
 * Generate canonical URL for content
 */
function getCanonicalUrl($content, $type = 'content') {
    $base_url = 'https://abla.lat';
    
    if (empty($content['url_slug'])) {
        return $base_url;
    }
    
    switch ($type) {
        case 'events':
            return $base_url . '/eventos/' . $content['url_slug'];
        case 'initiatives':
            return $base_url . '/iniciativas/' . $content['url_slug'];
        case 'content':
        default:
            return $base_url . '/' . $content['url_slug'];
    }
}

/**
 * Generate breadcrumbs for content
 */
function generateContentBreadcrumbs($content, $type = 'content') {
    $breadcrumbs = [
        ['name' => 'Inicio', 'url' => 'https://abla.lat']
    ];
    
    switch ($type) {
        case 'events':
            $breadcrumbs[] = ['name' => 'Eventos', 'url' => 'https://abla.lat/eventos'];
            $breadcrumbs[] = ['name' => $content['title'] ?? $content['name'], 'url' => getCanonicalUrl($content, $type)];
            break;
            
        case 'initiatives':
            $breadcrumbs[] = ['name' => 'Iniciativas', 'url' => 'https://abla.lat/iniciativas'];
            $breadcrumbs[] = ['name' => $content['name'], 'url' => getCanonicalUrl($content, $type)];
            break;
            
        case 'content':
        default:
            switch ($content['section']) {
                case 'manifesto':
                case 'why_abla':
                case 'statutes':
                case 'why_founder':
                case 'allies':
                case 'about':
                    $breadcrumbs[] = ['name' => 'Nosotros', 'url' => 'https://abla.lat/nosotros'];
                    break;
            }
            $breadcrumbs[] = ['name' => $content['title'], 'url' => getCanonicalUrl($content, $type)];
            break;
    }
    
    return $breadcrumbs;
}

/**
 * Get SEO meta tags for current content
 */
function getCurrentContentSEO() {
    if (isset($GLOBALS['seo_content'])) {
        $content = $GLOBALS['seo_content'];
        
        // Add canonical URL
        $content['canonical_url'] = getCanonicalUrl($content);
        
        return generateMetaTags($content);
    }
    
    return '';
}

/**
 * Get structured data for current content
 */
function getCurrentContentStructuredData() {
    if (isset($GLOBALS['seo_content'])) {
        $content = $GLOBALS['seo_content'];
        $breadcrumbs = generateContentBreadcrumbs($content);
        
        return generateBreadcrumbSchema($breadcrumbs);
    }
    
    return '';
}

/**
 * Check if current page is a content page
 */
function isContentPage() {
    return isset($GLOBALS['seo_content']);
}

/**
 * Get current page title
 */
function getCurrentPageTitle() {
    if (isset($GLOBALS['seo_content'])) {
        $content = $GLOBALS['seo_content'];
        return $content['meta_title'] ?: $content['title'] ?: getSEOSetting('default_meta_title');
    }
    
    return getSEOSetting('default_meta_title');
}

/**
 * Get current page description
 */
function getCurrentPageDescription() {
    if (isset($GLOBALS['seo_content'])) {
        $content = $GLOBALS['seo_content'];
        return $content['meta_description'] ?: getSEOSetting('default_meta_description');
    }
    
    return getSEOSetting('default_meta_description');
}

/**
 * Generate sitemap XML
 */
function generateSitemap() {
    $db = connectDB();
    if (is_array($db)) return '';
    
    $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    // Add homepage
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>https://abla.lat/</loc>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Add content pages
    $result = $db->query("SELECT url_slug, updated_at FROM content WHERE url_slug IS NOT NULL AND url_slug != '' AND is_published = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>https://abla.lat/" . htmlspecialchars($row['url_slug']) . "</loc>\n";
            if ($row['updated_at']) {
                $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($row['updated_at'])) . "</lastmod>\n";
            }
            $sitemap .= "    <changefreq>weekly</changefreq>\n";
            $sitemap .= "    <priority>0.8</priority>\n";
            $sitemap .= "  </url>\n";
        }
    }
    
    // Add events
    $result = $db->query("SELECT url_slug, updated_at FROM events WHERE url_slug IS NOT NULL AND url_slug != '' AND is_published = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>https://abla.lat/eventos/" . htmlspecialchars($row['url_slug']) . "</loc>\n";
            if ($row['updated_at']) {
                $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($row['updated_at'])) . "</lastmod>\n";
            }
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.6</priority>\n";
            $sitemap .= "  </url>\n";
        }
    }
    
    // Add initiatives
    $result = $db->query("SELECT url_slug, updated_at FROM initiatives WHERE url_slug IS NOT NULL AND url_slug != '' AND is_published = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>https://abla.lat/iniciativas/" . htmlspecialchars($row['url_slug']) . "</loc>\n";
            if ($row['updated_at']) {
                $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($row['updated_at'])) . "</lastmod>\n";
            }
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.6</priority>\n";
            $sitemap .= "  </url>\n";
        }
    }
    
    $sitemap .= "</urlset>\n";
    
    $db->close();
    return $sitemap;
}

/**
 * Generate robots.txt
 */
function generateRobotsTxt() {
    $robots = "User-agent: *\n";
    $robots .= "Allow: /\n";
    $robots .= "Disallow: /admin/\n";
    $robots .= "Disallow: /includes/\n";
    $robots .= "Disallow: /config.php\n";
    $robots .= "\n";
    $robots .= "Sitemap: https://abla.lat/sitemap.xml\n";
    
    // Add custom robots.txt content
    $custom = getSEOSetting('robots_txt_custom');
    if (!empty($custom)) {
        $robots .= "\n" . $custom . "\n";
    }
    
    return $robots;
}
?>
