<?php
/**
 * Social Media Platform Detection and Icon Management
 * Automatically detects social media platforms and provides appropriate icons
 */

/**
 * Detect social media platform from URL or username
 */
function detectSocialMediaPlatform($url_or_username) {
    $url = strtolower(trim($url_or_username));
    
    // Remove @ symbol if present
    $url = ltrim($url, '@');
    
    // Platform detection patterns
    $patterns = [
        'instagram' => [
            'instagram.com',
            'instagr.am',
            'ig.me'
        ],
        'twitter' => [
            'twitter.com',
            'x.com',
            't.co'
        ],
        'facebook' => [
            'facebook.com',
            'fb.com',
            'fb.me'
        ],
        'linkedin' => [
            'linkedin.com',
            'lnkd.in'
        ],
        'tiktok' => [
            'tiktok.com',
            'tiktok.app'
        ],
        'youtube' => [
            'youtube.com',
            'youtu.be',
            'youtube.app'
        ],
        'telegram' => [
            'telegram.org',
            't.me'
        ],
        'whatsapp' => [
            'whatsapp.com',
            'wa.me'
        ],
        'discord' => [
            'discord.com',
            'discord.gg'
        ]
    ];
    
    foreach ($patterns as $platform => $domains) {
        foreach ($domains as $domain) {
            if (strpos($url, $domain) !== false) {
                return $platform;
            }
        }
    }
    
    return 'generic';
}

/**
 * Get social media icon class for Phosphor Icons
 */
function getSocialMediaIcon($platform) {
    $icons = [
        'instagram' => 'ph-instagram-logo',
        'twitter' => 'ph-x-logo',
        'facebook' => 'ph-facebook-logo',
        'linkedin' => 'ph-linkedin-logo',
        'tiktok' => 'ph-tiktok-logo',
        'youtube' => 'ph-youtube-logo',
        'telegram' => 'ph-telegram-logo',
        'whatsapp' => 'ph-whatsapp-logo',
        'discord' => 'ph-discord-logo',
        'generic' => 'ph-link'
    ];
    
    return $icons[$platform] ?? $icons['generic'];
}

/**
 * Get social media platform display name
 */
function getSocialMediaDisplayName($platform) {
    $names = [
        'instagram' => 'Instagram',
        'twitter' => 'X',
        'facebook' => 'Facebook',
        'linkedin' => 'LinkedIn',
        'tiktok' => 'TikTok',
        'youtube' => 'YouTube',
        'telegram' => 'Telegram',
        'whatsapp' => 'WhatsApp',
        'discord' => 'Discord',
        'generic' => 'Link'
    ];
    
    return $names[$platform] ?? $names['generic'];
}

/**
 * Get social media platform color
 */
function getSocialMediaColor($platform) {
    $colors = [
        'instagram' => '#E4405F',
        'twitter' => '#000000',
        'facebook' => '#1877F2',
        'linkedin' => '#0A66C2',
        'tiktok' => '#000000',
        'youtube' => '#FF0000',
        'telegram' => '#0088CC',
        'whatsapp' => '#25D366',
        'discord' => '#5865F2',
        'generic' => '#6B7280'
    ];
    
    return $colors[$platform] ?? $colors['generic'];
}

/**
 * Generate social media links HTML for an initiative
 */
function generateSocialMediaLinks($initiative, $size = 'sm') {
    $links = [];
    
    // Define social media fields mapping
    $social_fields = [
        'instagram_link' => 'instagram_user',
        'twitter_link' => 'twitter_user',
        'facebook_link' => null,
        'linkedin_link' => null,
        'tiktok_link' => null,
        'youtube_link' => null,
        'website_link' => null
    ];
    
    foreach ($social_fields as $link_field => $user_field) {
        if (!empty($initiative[$link_field])) {
            $url = $initiative[$link_field];
            $platform = detectSocialMediaPlatform($url);
            $icon = getSocialMediaIcon($platform);
            $name = getSocialMediaDisplayName($platform);
            $color = getSocialMediaColor($platform);
            
            // Get username if available
            $username = '';
            if ($user_field && !empty($initiative[$user_field])) {
                $username = $initiative[$user_field];
                if (!str_starts_with($username, '@')) {
                    $username = '@' . $username;
                }
            }
            
            $display_text = $username ?: $name;
            
            $links[] = [
                'url' => $url,
                'platform' => $platform,
                'icon' => $icon,
                'name' => $name,
                'color' => $color,
                'display_text' => $display_text
            ];
        }
    }
    
    return $links;
}

/**
 * Render social media links as HTML buttons
 */
function renderSocialMediaLinks($initiative, $size = 'sm') {
    $links = generateSocialMediaLinks($initiative, $size);
    $html = '';
    
    foreach ($links as $link) {
        $html .= sprintf(
            '<a href="%s" target="_blank" rel="noopener noreferrer" class="btn btn-secondary btn-%s social-link" data-platform="%s" title="%s">
                <i class="ph %s"></i> %s
            </a>',
            htmlspecialchars($link['url']),
            $size,
            $link['platform'],
            htmlspecialchars($link['name']),
            $link['icon'],
            htmlspecialchars($link['display_text'])
        );
    }
    
    return $html;
}
?>
