<?php
// Get hero content from database
$db = connectDB();
$hero = $db->query("SELECT * FROM content WHERE section = 'hero' LIMIT 1");

$hero_content = null;
if ($hero && $hero->num_rows > 0) {
    $hero_content = $hero->fetch_assoc();
}

// Include social media detector for enhanced functionality
require_once 'includes/social_media_detector.php';
?>

<!-- Hero Section -->
<section id="home" class="min-h-[80vh] md:min-h-[85vh] flex items-start md:items-center justify-center bg-black text-white pt-8 md:pt-12 pb-12 md:pb-16 px-4 relative overflow-hidden">
    <!-- Bitcoin Network Background -->
    <div class="bitcoin-network" id="bitcoin-network"></div>

    <!-- Bitcoin Particles Background -->
    <div class="bitcoin-particles" id="bitcoin-particles"></div>

    <div class="container mx-auto max-w-6xl relative z-10 mt-4 md:mt-0">
        <!-- Bitcoin Price Ticker - Moved to avoid text overlap -->
        <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-floating-x z-20 hidden sm:block" style="animation-delay: 1.5s;">
            <div class="price-ticker" id="bitcoin-price">
                <i class="ph ph-currency-btc text-orange-500"></i>
                <span class="price-value" id="btc-price">$45,231.67</span>
                <span class="text-xs text-green-500" id="btc-change">****%</span>
            </div>
        </div>

        <!-- Mobile Price Ticker -->
        <div class="sm:hidden mb-4 flex justify-center animate-fadeIn" style="animation-delay: 1.5s;">
            <div class="price-ticker" id="bitcoin-price-mobile">
                <i class="ph ph-currency-btc text-orange-500"></i>
                <span class="price-value" id="btc-price-mobile">$45,231.67</span>
                <span class="text-xs text-green-500" id="btc-change-mobile">****%</span>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div class="animate-fadeIn relative z-20">
                <!-- Text background for better visibility -->
                <div class="absolute inset-0 bg-black/15 backdrop-blur-sm rounded-2xl -m-3 p-3 opacity-0 animate-fadeIn" style="animation-delay: 0.2s; animation-fill-mode: forwards;"></div>

                <div class="relative z-10">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold uppercase mb-4 md:mb-6 leading-tight">
                        <?php if ($hero_content && !empty($hero_content['title'])): ?>
                            <?php echo $hero_content['title']; ?>
                        <?php else: ?>
                            ASOCIACIÓN <span class="text-orange-500 animate-text-glow">BITCOIN</span> LATINOAMÉRICANA
                        <?php endif; ?>
                    </h1>

                    <p class="text-lg md:text-xl mb-6 md:mb-8 text-gray-200 animate-slideIn leading-relaxed" style="animation-delay: 0.3s;">
                        <?php if ($hero_content && !empty($hero_content['content'])): ?>
                            <?php echo $hero_content['content']; ?>
                        <?php else: ?>
                            Promoviendo la adopción de Bitcoin en Latinoamérica a través de educación, desarrollo y colaboración entre comunidades.
                        <?php endif; ?>
                    </p>
                </div>

                <div class="relative z-10">
                    <div class="flex flex-wrap gap-4 animate-slideIn" style="animation-delay: 0.6s;">
                        <a href="#descubre" class="btn btn-primary smooth-scroll">
                            <i class="ph ph-compass"></i> Descubre ABLA
                        </a>
                        <a href="#sumate" class="btn btn-secondary smooth-scroll">
                            <i class="ph ph-users"></i> Súmate
                        </a>
                    </div>
                </div>

                <!-- Lightning Network Animation -->
                <div class="mt-12 relative h-24 hidden md:block">
                    <div class="absolute left-0 top-0 animate-floating-xy" style="animation-delay: 0.2s;">
                        <div class="relative">
                            <i class="ph ph-currency-btc text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-24 top-8 animate-floating-xy" style="animation-delay: 0.7s;">
                        <div class="relative">
                            <i class="ph ph-lightning text-orange-500 text-3xl animate-lightning" style="--delay: 0.5"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-48 top-2 animate-floating-xy" style="animation-delay: 1.2s;">
                        <div class="relative">
                            <i class="ph ph-wallet text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-72 top-10 animate-floating-xy" style="animation-delay: 0.5s;">
                        <div class="relative">
                            <i class="ph ph-globe text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <!-- Lightning connections -->
                    <svg class="absolute top-0 left-0 w-full h-full" style="z-index: -1;">
                        <line id="line1" x1="12" y1="12" x2="96" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line2" x1="96" y1="40" x2="192" y2="12" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line3" x1="192" y1="12" x2="288" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line4" x1="12" y1="12" x2="288" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" stroke-opacity="0.3" />

                        <defs>
                            <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#F7931A" stop-opacity="0.2" />
                                <stop offset="50%" stop-color="#F7931A" stop-opacity="1" />
                                <stop offset="100%" stop-color="#F7931A" stop-opacity="0.2" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>

                <!-- Bitcoin Stats -->
                <div class="relative z-10">
                    <div class="mt-8 grid grid-cols-3 gap-4 animate-slideIn" style="animation-delay: 0.9s;">
                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Hashrate</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-hash text-orange-500 text-xs"></i>
                                <span class="text-white">512 EH/s</span>
                            </div>
                        </div>

                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Dificultad</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-chart-line-up text-orange-500 text-xs"></i>
                                <span class="text-white">72.8T</span>
                            </div>
                        </div>

                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Bloque</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-cube text-orange-500 text-xs"></i>
                                <span class="text-white">#823,456</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center lg:justify-end">
                <!-- Bitcoin animation removed -->
            </div>
        </div>
    </div>
</section>

<!-- Add JavaScript for Bitcoin animations -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bitcoin particles
    const particlesContainer = document.getElementById('bitcoin-particles');
    const particleCount = 20;

    // Create Bitcoin particles
    for (let i = 0; i < particleCount; i++) {
        createParticle();
    }

    function createParticle() {
        const particle = document.createElement('div');
        particle.classList.add('bitcoin-particle');

        // Random position, size and animation duration
        const size = Math.random() * 15 + 5;
        const left = Math.random() * 100;
        const delay = Math.random() * 5;
        const duration = Math.random() * 10 + 10;

        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${left}%`;
        particle.style.bottom = '-20px';
        particle.style.opacity = Math.random() * 0.3;
        particle.style.animation = `particleFloat ${duration}s linear infinite`;
        particle.style.animationDelay = `${delay}s`;

        // Use Bitcoin symbol for some particles
        if (Math.random() > 0.7) {
            particle.innerHTML = '<i class="ph ph-currency-btc" style="color: #F7931A; font-size: ' + size + 'px;"></i>';
            particle.style.background = 'transparent';
        }

        particlesContainer.appendChild(particle);
    }

    // Bitcoin network animation
    const networkContainer = document.getElementById('bitcoin-network');
    const nodeCount = 8;
    const nodes = [];

    // Create network nodes
    for (let i = 0; i < nodeCount; i++) {
        createNode(i);
    }

    function createNode(index) {
        const node = document.createElement('div');
        node.classList.add('network-node');

        // Position nodes in a grid-like pattern
        const row = Math.floor(index / 3);
        const col = index % 3;

        // Add some randomness to positions
        const xPos = 20 + col * 30 + (Math.random() * 10 - 5);
        const yPos = 20 + row * 30 + (Math.random() * 10 - 5);

        node.style.left = `${xPos}%`;
        node.style.top = `${yPos}%`;

        networkContainer.appendChild(node);
        nodes.push({
            element: node,
            x: xPos,
            y: yPos
        });
    }

    // Create connections between nodes
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            // Only connect some nodes (not all-to-all)
            if (Math.random() > 0.7) {
                createConnection(nodes[i], nodes[j]);
            }
        }
    }

    function createConnection(nodeA, nodeB) {
        const line = document.createElement('div');
        line.classList.add('network-line');

        // Calculate line position and length
        const dx = nodeB.x - nodeA.x;
        const dy = nodeB.y - nodeA.y;
        const length = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;

        line.style.width = `${length}%`;
        line.style.left = `${nodeA.x}%`;
        line.style.top = `${nodeA.y}%`;
        line.style.transform = `rotate(${angle}deg)`;
        line.style.animationDelay = `${Math.random() * 5}s`;

        networkContainer.appendChild(line);
    }

    // Bitcoin price ticker animation
    const priceElement = document.getElementById('btc-price');
    const changeElement = document.getElementById('btc-change');
    const priceElementMobile = document.getElementById('btc-price-mobile');
    const changeElementMobile = document.getElementById('btc-change-mobile');

    // Simulate price updates
    function updatePrice() {
        const currentPrice = parseFloat(priceElement.textContent.replace('$', '').replace(',', ''));
        const change = (Math.random() - 0.45) * 100; // Slightly biased towards positive
        const newPrice = currentPrice + change;

        // Format price
        const formattedPrice = '$' + newPrice.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        // Update change percentage
        const changePercent = (change / currentPrice * 100).toFixed(2);
        const isPositive = changePercent >= 0;
        const changeText = (isPositive ? '+' : '') + changePercent + '%';
        const changeClass = 'text-xs ' + (isPositive ? 'text-green-500' : 'text-red-500');

        // Update desktop ticker
        if (priceElement) {
            priceElement.textContent = formattedPrice;
            changeElement.textContent = changeText;
            changeElement.className = changeClass;

            // Add animation class
            priceElement.classList.remove('ticker-up', 'ticker-down');
            void priceElement.offsetWidth; // Trigger reflow
            priceElement.classList.add(isPositive ? 'ticker-up' : 'ticker-down');
        }

        // Update mobile ticker
        if (priceElementMobile) {
            priceElementMobile.textContent = formattedPrice;
            changeElementMobile.textContent = changeText;
            changeElementMobile.className = changeClass;

            // Add animation class
            priceElementMobile.classList.remove('ticker-up', 'ticker-down');
            void priceElementMobile.offsetWidth; // Trigger reflow
            priceElementMobile.classList.add(isPositive ? 'ticker-up' : 'ticker-down');
        }

        // Schedule next update
        setTimeout(updatePrice, Math.random() * 5000 + 3000);
    }

    // Initial delay before starting price updates
    setTimeout(updatePrice, 3000);

    // Animate SVG lightning lines
    const lines = document.querySelectorAll('#line1, #line2, #line3, #line4');

    lines.forEach((line, index) => {
        // Create animation for each line
        const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
        animate.setAttribute('attributeName', 'stroke-dashoffset');
        animate.setAttribute('from', '10');
        animate.setAttribute('to', '0');
        animate.setAttribute('dur', '1.5s');
        animate.setAttribute('repeatCount', 'indefinite');

        line.appendChild(animate);

        // Add some delay between animations
        animate.beginElement();
        setTimeout(() => {
            animate.beginElement();
        }, index * 400);
    });
});
</script>

<script>
// Initiatives Carousel
const carousel = document.querySelector('.carousel-container');
const prevBtn = document.querySelector('.carousel-prev');
const nextBtn = document.querySelector('.carousel-next');
const cards = document.querySelectorAll('.card');
let currentIndex = 0;

// Initialize Hammer.js for touch support
if (typeof Hammer !== 'undefined') {
    const hammer = new Hammer(carousel);
    hammer.on('swipeleft', () => nextCard());
    hammer.on('swiperight', () => prevCard());
}

function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 16; // width + margin
    carousel.scrollTo({
        left: currentIndex * cardWidth,
        behavior: 'smooth'
    });
}

function nextCard() {
    if (currentIndex < cards.length - 1) {
        currentIndex++;
        updateCarousel();
    }
}

function prevCard() {
    if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
    }
}

prevBtn.addEventListener('click', prevCard);
nextBtn.addEventListener('click', nextCard);

// Auto-scroll every 5 seconds
let autoScroll = setInterval(nextCard, 5000);

// Pause auto-scroll on hover/interaction
carousel.addEventListener('mouseenter', () => clearInterval(autoScroll));
carousel.addEventListener('mouseleave', () => {
    autoScroll = setInterval(nextCard, 5000);
});

// Infinite scroll (loop back to start)
carousel.addEventListener('scrollend', () => {
    if (currentIndex >= cards.length - 3) {
        setTimeout(() => {
            currentIndex = 0;
            updateCarousel();
        }, 3000);
    }
});
</script>

<!-- Enhanced Initiatives Section with Pagination -->
<section id="descubre" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <h2 class="text-3xl font-bold text-center mb-12">Iniciativas <span class="text-orange-500">Bitcoin</span> en Latinoamérica</h2>

        <!-- Enhanced Initiatives Container with Pagination -->
        <div id="initiatives-container">
            <!-- Content will be loaded dynamically by JavaScript -->
            <div class="initiatives-loading">
                <div class="spinner"></div>
                <span>Cargando iniciativas...</span>
            </div>
        </div>

        <div class="text-center mt-12">
            <a href="#contacto" class="btn btn-primary smooth-scroll">
                <i class="ph ph-plus-circle"></i> ¡CONTANOS SOBRE TU INICIATIVA!
            </a>
        </div>
    </div>
</section>

<!-- Manifesto Section -->
<section id="manifesto" class="py-16 px-4" style="background-color: var(--dark-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-6">Nuestro <span class="text-orange-500">Manifiesto</span></h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">Los principios y valores que guían nuestra misión de promover Bitcoin en Latinoamérica</p>
        </div>

        <!-- Manifesto Introduction -->
        <div class="card mb-12">
            <div class="prose prose-lg max-w-none text-gray-300">
                <p class="text-xl leading-relaxed mb-6">
                    <strong class="text-orange-500">Latinoamérica despierta.</strong> Nuestros pueblos, desde el Río Bravo hasta la Patagonia, han soportado décadas de desigualdad, inflación y exclusión financiera que erosiona la dignidad día tras día. Hemos visto cómo la moneda inflacionaria actúa como un impuesto silencioso que empobrece a las familias y socava el fruto de su trabajo. Frente a esta realidad, nos alzamos en una resistencia pacífica: abrazamos a Bitcoin como vía de emancipación económica y social. ABLA nace como un grito de esperanza y libertad, una respuesta comunitaria al sistema monetario que ya no nos representa.
                </p>

                <p class="text-lg leading-relaxed mb-6">
                    <strong class="text-white">Asociación Bitcoin Latinoamérica (ABLA)</strong> es una red libre de organizaciones, iniciativas y personas unidas por un ideal común: acelerar la adopción de Bitcoin en la región para devolver el poder financiero a la gente. Inspirados en la pedagogía liberadora de Paulo Freire, entendemos que la verdadera transformación nace de la educación consciente y popular. <em class="text-orange-400">"Nadie libera a nadie, ni nadie se libera solo. Los hombres se liberan en comunión"</em> – hacemos nuestras estas palabras, convencidos de que solo mediante la unión comunitaria y el conocimiento compartido lograremos nuestra liberación financiera.
                </p>

                <p class="text-lg leading-relaxed mb-8">
                    <strong class="text-white">Bitcoin no es solamente una moneda: es un movimiento.</strong> Representa la soberanía individual y la cooperación voluntaria entre pares. En un mundo donde los bancos centrales y gobiernos nos han fallado con políticas que devalúan nuestras monedas, Bitcoin surge como un instrumento de autonomía. Su naturaleza descentralizada nos permite intercambiar valor sin intermediarios, sin permisos y sin fronteras, devolviendo a cada persona el control sobre el fruto de su trabajo.
                </p>
            </div>
        </div>

        <!-- Core Values -->
        <div class="mb-12">
            <h3 class="text-2xl font-bold text-center mb-8 text-white">Nuestros <span class="text-orange-500">Valores Fundacionales</span></h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-users text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Empoderamiento Comunitario</h4>
                    <p class="text-gray-300">Creemos en el poder de la gente organizada. Fomentamos la colaboración local y regional, formando redes de apoyo donde cada barrio, cada comunidad rural y cada ciudad pueda aprender y utilizar Bitcoin para mejorar sus vidas.</p>
                </div>

                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-heart text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Inclusión Financiera</h4>
                    <p class="text-gray-300">Luchamos por cerrar brechas históricas. Bitcoin, al ser de acceso abierto, ofrece oportunidad a quienes quedaron fuera del sistema tradicional: al campesino sin banco, a la emprendedora sin crédito, al migrante con sueños.</p>
                </div>

                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-graduation-cap text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Reducción de Brechas Tecnológicas</h4>
                    <p class="text-gray-300">La alfabetización digital y financiera es nuestra bandera. Siguiendo el legado de Freire, promovemos una educación popular que demuele la figura del oprimido tecnológico.</p>
                </div>

                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-lightning text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Soberanía Financiera</h4>
                    <p class="text-gray-300">Tomamos en nuestras manos la autonomía económica de nuestros países e individuos. Cada persona tiene derecho a ahorrar en un dinero sólido que no pueda ser devaluado a voluntad por terceros.</p>
                </div>

                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-shield-check text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Resistencia Pacífica</h4>
                    <p class="text-gray-300">Nuestra revolución es pacífica, pero firme. No empuñamos armas, sino códigos, educación y solidaridad. Bitcoin es nuestra forma de protesta no violenta contra un orden económico que ha fallado a las mayorías.</p>
                </div>

                <div class="card">
                    <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mb-4 flex items-center justify-center">
                        <i class="ph ph-flag text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-3 text-white">Segunda Independencia</h4>
                    <p class="text-gray-300">Ha llegado la hora de nuestra segunda independencia: la financiera. Así como nuestros antepasados forjaron la libertad política, esta generación forjará la libertad monetaria.</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="card bg-gradient-to-r from-orange-500/20 to-orange-600/20 border-orange-500/30">
                <h3 class="text-2xl font-bold mb-4 text-white">¡Únete a la Revolución!</h3>
                <p class="text-lg text-gray-300 mb-6">
                    Con conocimiento, empatía y determinación, construiremos una Latinoamérica donde el dinero vuelva a ser del pueblo. Unidos, más allá de las fronteras, tomamos en nuestras manos nuestro destino económico.
                </p>
                <a href="#por-que-abla" class="btn btn-primary smooth-scroll">
                    <i class="ph ph-arrow-down mr-2"></i>Descubre Por Qué ABLA
                </a>
            </div>
        </div>
    </div>
</section>

<!-- About Us Section -->
<section id="nosotros" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-3xl font-bold mb-6">Sobre <span class="text-orange-500">ABLA</span></h2>
                <p class="text-lg mb-6">La Asociación Bitcoin Latinoamérica (ABLA) es una organización sin fines de lucro dedicada a promover la adopción de Bitcoin en toda América Latina.</p>

                <div class="space-y-4 mb-8">
                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-graduation-cap text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Educación</h3>
                            <p>Desarrollamos programas educativos para difundir el conocimiento sobre Bitcoin y su tecnología subyacente.</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-handshake text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Colaboración</h3>
                            <p>Conectamos iniciativas Bitcoin en toda la región para fortalecer el ecosistema latinoamericano.</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-lightbulb text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Innovación</h3>
                            <p>Apoyamos proyectos innovadores que utilizan Bitcoin para resolver problemas reales en nuestras comunidades.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center">
                <div class="relative">
                    <div class="w-full max-w-md h-80 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg overflow-hidden">
                        <div class="absolute inset-0 opacity-30">
                            <div class="absolute top-0 left-0 w-full h-full bg-repeat" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjZjc5MzFhIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0wIDBoNDB2NDBoLTQweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xMCAxMGgyMHYyMGgtMjB6IiBmaWxsLW9wYWNpdHk9Ii4yIi8+PC9nPjwvc3ZnPg==');"></div>
                        </div>

                        <div class="relative z-10 flex flex-col items-center justify-center h-full p-6 text-center">
                            <h3 class="text-2xl font-bold mb-2">Misión</h3>
                            <p class="text-gray-300">Impulsar la adopción de Bitcoin en Latinoamérica para promover la inclusión financiera, la soberanía individual y el desarrollo económico.</p>
                        </div>
                    </div>

                    <!-- Decorative elements -->
                    <div class="absolute -top-4 -right-4 w-12 h-12 bg-orange-500 rounded-lg rotate-12"></div>
                    <div class="absolute -bottom-4 -left-4 w-8 h-8 bg-orange-500 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- ¿Por qué ABLA? Section -->
<section id="por-que-abla" class="py-16 px-4" style="background-color: var(--dark-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-6">¿Por qué <span class="text-orange-500">ABLA</span>?</h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">ABLA existe porque Latinoamérica necesita una voz y una fuerza propia en la revolución Bitcoin</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
            <div>
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-white">Desafíos Únicos de Nuestra Región</h3>
                    <div class="space-y-4">
                        <div class="flex items-start gap-4">
                            <div class="bg-red-500 rounded-full p-2 mt-1">
                                <i class="ph ph-trend-down text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Economías Golpeadas por la Inflación</h4>
                                <p class="text-gray-300">Nuestros países enfrentan constantes devaluaciones monetarias que erosionan el poder adquisitivo de las familias.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="bg-red-500 rounded-full p-2 mt-1">
                                <i class="ph ph-bank text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Poblaciones No Bancarizadas</h4>
                                <p class="text-gray-300">Alto porcentaje de personas sin acceso al sistema financiero tradicional.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="bg-red-500 rounded-full p-2 mt-1">
                                <i class="ph ph-money text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Remesas Costosas</h4>
                                <p class="text-gray-300">El costo exorbitante de enviar remesas que sostienen a millones de familias.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-white">Oportunidades de Bitcoin</h3>
                    <div class="space-y-4">
                        <div class="flex items-start gap-4">
                            <div class="bg-green-500 rounded-full p-2 mt-1">
                                <i class="ph ph-chart-line-up text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Adopción Acelerada</h4>
                                <p class="text-gray-300">Latinoamérica ha demostrado ser terreno fértil para la adopción de Bitcoin debido a estas mismas dificultades.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="bg-green-500 rounded-full p-2 mt-1">
                                <i class="ph ph-shield text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Protección de Ahorros</h4>
                                <p class="text-gray-300">Muchos recurren a Bitcoin para proteger sus ahorros de la inflación y la inestabilidad.</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="bg-green-500 rounded-full p-2 mt-1">
                                <i class="ph ph-users-three text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-white">Soluciones Locales</h4>
                                <p class="text-gray-300">Nadie entiende nuestras necesidades mejor que nosotros mismos.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-12">
            <h3 class="text-2xl font-bold mb-6 text-center text-white">Una Red <span class="text-orange-500">Específicamente Latinoamericana</span></h3>
            <div class="prose prose-lg max-w-none text-gray-300">
                <p class="text-lg leading-relaxed mb-6">
                    ¿Por qué una red latinoamericana y no una global? Porque <strong class="text-white">compartimos historia, idioma y retos comunes</strong>. Desde el Caribe hasta la Patagonia, conocemos de primera mano el impacto de las devaluaciones monetarias, la desigualdad y la ausencia de oportunidades financieras. Sabemos que la respuesta global a veces no alcanza a escuchar las voces locales.
                </p>

                <p class="text-lg leading-relaxed mb-6">
                    ABLA viene a <strong class="text-orange-500">tejer lazos entre naciones hermanas</strong>: unir a El Salvador con Argentina, a México con Colombia, a Venezuela con Guatemala, y a todos los países de la región, en una colaboración interregional sin precedentes. Juntos podemos intercambiar aprendizajes y recursos, coordinar esfuerzos educativos y amplificar el impacto de iniciativas que ya están cambiando vidas en comunidades locales.
                </p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-lightning text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Acelerar lo que ya Comenzó</h4>
                <p class="text-gray-300">La adopción de Bitcoin en Latinoamérica no es una teoría, es un fenómeno en marcha que necesita coordinación.</p>
            </div>

            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-network text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Conectar Iniciativas</h4>
                <p class="text-gray-300">Conectamos puntos luminosos en un mapa común, creando una constelación que guíe a toda la región.</p>
            </div>

            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-megaphone text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Voz Propia</h4>
                <p class="text-gray-300">Latinoamérica habla con voz propia sobre su futuro financiero, liderando la revolución con perspectiva humanista.</p>
            </div>
        </div>

        <div class="text-center">
            <div class="card bg-gradient-to-r from-orange-500/20 to-orange-600/20 border-orange-500/30">
                <h3 class="text-2xl font-bold mb-4 text-white">La Unión Hace la Fuerza</h3>
                <p class="text-lg text-gray-300 mb-6">
                    ABLA es el espacio donde construiremos puentes entre activistas, desarrolladores, educadores, empresarios y cualquier persona que comparta la visión de una economía más libre. Juntos, representamos un bloque diverso pero unido.
                </p>
                <a href="#estatutos" class="btn btn-primary smooth-scroll">
                    <i class="ph ph-scroll mr-2"></i>Conoce Nuestros Estatutos
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Estatutos Section -->
<section id="estatutos" class="py-16 px-4" style="background-color: var(--dark-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-6">Estatutos <span class="text-orange-500">ABLA</span></h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">Principios organizativos que reflejan el espíritu descentralizado de Bitcoin en nuestra gobernanza</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div class="card">
                <div class="bg-orange-500 rounded-lg p-4 mb-4 inline-block">
                    <i class="ph ph-tree-structure text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3 text-white">Estructura Mínima</h3>
                <p class="text-gray-300 mb-4">ABLA funcionará con la estructura organizativa más sencilla posible, fomentando una dinámica horizontal y participativa. Se establecerá un Consejo Coordinador interregional encargado de articular las iniciativas y proyectos, pero evitando toda burocracia innecesaria.</p>
                <p class="text-gray-300">La toma de decisiones será ágil y distribuida, reflejando el espíritu descentralizado de Bitcoin en la gobernanza de la asociación.</p>
            </div>

            <div class="card">
                <div class="bg-orange-500 rounded-lg p-4 mb-4 inline-block">
                    <i class="ph ph-calendar text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3 text-white">Elecciones cada 2 años</h3>
                <p class="text-gray-300 mb-4">Los cargos de coordinación y representación en ABLA serán temporales. Cada dos años se realizan elecciones democráticas para renovar o revalidar a los integrantes del Consejo Coordinador u otras posiciones de liderazgo definidas.</p>
                <p class="text-gray-300">Este periodo bienal asegura rotación, transparencia y la oportunidad de incorporar ideas frescas regularmente. Nadie se "atornillará" en un puesto: la representación en ABLA es un servicio, no un privilegio vitalicio.</p>
            </div>

            <div class="card">
                <div class="bg-orange-500 rounded-lg p-4 mb-4 inline-block">
                    <i class="ph ph-devices text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3 text-white">Votaciones en Línea</h3>
                <p class="text-gray-300 mb-4">Aprovechando la tecnología, todas las votaciones de ABLA – ya sean elecciones de representantes, decisiones sobre proyectos o cualquier asunto relevante – se llevarán a cabo de forma online.</p>
                <p class="text-gray-300">Se implementarán plataformas seguras, verificables y accesibles para que todos los miembros puedan ejercer su voto desde cualquier país. Esto garantiza inclusión geográfica y participación masiva, reduciendo costos y facilitando la democracia directa.</p>
            </div>

            <div class="card">
                <div class="bg-orange-500 rounded-lg p-4 mb-4 inline-block">
                    <i class="ph ph-check-circle text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3 text-white">Derecho a Voto Amplio</h3>
                <p class="text-gray-300 mb-4">¿Quiénes pueden votar? Todos los miembros activos de ABLA, ya sean individuos o representantes de organizaciones miembro. Cada persona u organización registrada en la asociación con pleno derecho de participación tendrá un voto en las decisiones colectivas.</p>
                <p class="text-gray-300">No se restringirá el voto por nivel educativo, poder adquisitivo ni procedencia; al contrario, ABLA se construye sobre la premisa de la inclusión, por lo que todas las voces valen por igual.</p>
            </div>
        </div>

        <div class="card mb-12">
            <div class="bg-orange-500 rounded-lg p-4 mb-4 inline-block">
                <i class="ph ph-chat-text text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold mb-4 text-white">Comunicación Clara y Sencilla</h3>
            <div class="prose prose-lg max-w-none text-gray-300">
                <p class="text-lg leading-relaxed mb-4">
                    Todos los documentos, estatutos, comunicaciones y materiales educativos de ABLA estarán redactados en un <strong class="text-white">lenguaje claro, sencillo y accesible</strong>. Evitaremos la jerga técnica o legal compleja para que cualquier persona, sin importar su nivel de formación, pueda entender y participar plenamente.
                </p>

                <p class="text-lg leading-relaxed mb-4">
                    La transparencia comienza por la comprensión: en ABLA nos aseguramos de que nuestras reglas y mensajes sean para todos. Hablaremos al público en <strong class="text-orange-500">español latinoamericano neutro</strong>, y promoveremos la traducción o adaptación cultural necesaria para integrar a comunidades de distintos idiomas en la región.
                </p>

                <p class="text-lg leading-relaxed">
                    La simplicidad en el lenguaje refuerza nuestro compromiso con la educación inclusiva y la participación informada.
                </p>
            </div>
        </div>

        <div class="text-center">
            <div class="card bg-gradient-to-r from-orange-500/20 to-orange-600/20 border-orange-500/30">
                <h3 class="text-2xl font-bold mb-4 text-white">Estatutos Evolutivos</h3>
                <p class="text-lg text-gray-300 mb-6">
                    Estos estatutos básicos sirven como guía fundacional. Podrán ampliarse o detallarse mediante consenso de los miembros, respetando siempre la esencia de apertura, descentralización y simplicidad que aquí se consagra.
                </p>
                <a href="#por-que-ser-fundador" class="btn btn-primary smooth-scroll">
                    <i class="ph ph-star mr-2"></i>¿Por Qué Ser Fundador?
                </a>
            </div>
        </div>
    </div>
</section>

<!-- ¿Por qué ser fundador? Section -->
<section id="por-que-ser-fundador" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-6">¿Por qué ser <span class="text-orange-500">Fundador</span>?</h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">Hasta el 31 de agosto recibiremos postulaciones para formar parte del equipo fundador</p>
            <div class="mt-6">
                <a href="#sumate" class="btn btn-primary btn-lg smooth-scroll">
                    <i class="ph ph-plus-circle mr-2"></i>Formá parte de ABLA
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
            <div>
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-white">Un Papel <span class="text-orange-500">Histórico</span></h3>
                    <p class="text-lg text-gray-300 mb-4">
                        Ser fundador de ABLA es mucho más que figurar en una lista: es <strong class="text-white">abrazar un papel histórico en la liberación financiera de Latinoamérica</strong>. Nos encontramos en un punto de inflexión que los libros de historia algún día destacarán – el amanecer de la bitconización de nuestra región.
                    </p>
                    <p class="text-lg text-gray-300">
                        Quienes den un paso al frente ahora, como miembros fundadores, estarán tomando el lugar que ocuparon los visionarios en otras épocas: aquellos que vieron nacer un nuevo paradigma y no se quedaron al margen, sino que lo forjaron con sus manos y su convicción.
                    </p>
                </div>
            </div>

            <div>
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-white">Nuevos <span class="text-orange-500">Libertadores</span></h3>
                    <p class="text-lg text-gray-300 mb-4">
                        Desde una perspectiva histórica, ser fundador de ABLA equivale a ser un <strong class="text-white">nuevo libertador</strong>. Nuestros próceres hace dos siglos soñaron con una América Latina libre de dominación política; hoy, nosotros luchamos por una Latinoamérica libre de ataduras financieras externas e injustas.
                    </p>
                    <p class="text-lg text-gray-300">
                        La historia nos llama a completar la obra iniciada por nuestros antepasados: conquistamos la independencia política, ahora vamos por la <strong class="text-orange-500">independencia monetaria</strong>.
                    </p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-heart text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Compromiso Moral</h4>
                <p class="text-gray-300">Ser miembro fundador es un compromiso profundo con los valores de justicia, equidad y solidaridad. Significa alzar la voz por aquellos que han sido silenciados por la pobreza y la exclusión.</p>
            </div>

            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-gear text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Pionero Técnico</h4>
                <p class="text-gray-300">Un fundador de ABLA es un pionero que reconoce el potencial transformador de la tecnología Bitcoin en nuestro contexto, adaptándola a nuestros entornos locales.</p>
            </div>

            <div class="card text-center">
                <div class="bg-orange-500 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="ph ph-trophy text-white text-2xl"></i>
                </div>
                <h4 class="text-xl font-bold mb-3 text-white">Legado Histórico</h4>
                <p class="text-gray-300">Ser fundador conlleva el honor de construir los cimientos de una institución que perdurará por décadas como agente de cambio en Latinoamérica.</p>
            </div>
        </div>

        <div class="card mb-12">
            <h3 class="text-2xl font-bold mb-6 text-center text-white">La <span class="text-orange-500">Bitconización</span> ya Comenzó</h3>
            <div class="prose prose-lg max-w-none text-gray-300">
                <p class="text-lg leading-relaxed mb-6">
                    La bitconización de Latinoamérica ya ha comenzado, pero su éxito y aceleración dependerán de los valientes que den el paso al frente ahora. <strong class="text-white">Tú puedes ser uno de ellos</strong>. Te invitamos a hacer historia con nosotros, a escribir el primer capítulo de esta revolución pacífica.
                </p>

                <p class="text-lg leading-relaxed mb-6">
                    Ser fundador de ABLA significa ser <strong class="text-orange-500">arquitecto del mañana</strong>: un mañana donde la libertad financiera deje de ser un sueño y se convierta en la nueva realidad de nuestro continente. No importa tu profesión, tu edad o tu bandera nacional – en ABLA todos compartimos la bandera de la Bitcoinización y los colores de la unidad latinoamericana.
                </p>

                <p class="text-xl leading-relaxed text-center font-bold text-white">
                    ¡Por la libertad, la descentralización y la hermandad latinoamericana, construyamos juntos este legado revolucionario!
                </p>
            </div>
        </div>

        <div class="text-center">
            <div class="card bg-gradient-to-r from-orange-500/20 to-orange-600/20 border-orange-500/30">
                <h3 class="text-2xl font-bold mb-4 text-white">¡Súmate y Seamos los Protagonistas!</h3>
                <p class="text-lg text-gray-300 mb-6">
                    Si sientes en el pecho el llamado de la libertad, si te indigna la injusticia económica y a la vez te ilusiona el futuro que podemos crear, este es tu momento. Únete a nosotros como miembro fundador y ayúdanos a encender esta llama que ningún viento podrá apagar.
                </p>
                <a href="#sumate" class="btn btn-primary btn-lg smooth-scroll">
                    <i class="ph ph-rocket mr-2"></i>¡Ser Fundador de ABLA!
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="sumate" class="py-16 px-4 bg-black">
    <div class="container mx-auto max-w-4xl text-center">
        <h2 class="text-3xl font-bold mb-6">¿Quieres formar parte de ABLA?</h2>
        <p class="text-xl mb-8">Únete a nuestra comunidad y contribuye al crecimiento de Bitcoin en América Latina.</p>
        <a href="#contacto" class="btn btn-primary smooth-scroll">
            <i class="ph ph-users-three"></i> Súmate a ABLA
        </a>
    </div>
</section>

<!-- Contact Form Section -->
<section id="contacto" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
                <h2 class="text-3xl font-bold mb-6">Contacta con <span class="text-orange-500">Nosotros</span></h2>
                <p class="text-lg mb-8">¿Tienes preguntas o quieres formar parte de ABLA? Completa el formulario y nos pondremos en contacto contigo lo antes posible.</p>

                <div class="space-y-6">
                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-envelope text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Email</h3>
                            <p class="text-gray-300"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-map-pin text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Ubicación</h3>
                            <p class="text-gray-300">Latinoamérica</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-globe text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Redes Sociales</h3>
                            <div class="flex gap-4 mt-2">
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-twitter-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-instagram-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-telegram-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-github-logo text-2xl"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <form class="space-y-6" id="contactForm" action="/process_form.php" method="post">
                    <!-- Honeypot field to catch bots -->
                    <div style="display:none">
                        <label for="website">Website (leave this empty)</label>
                        <input type="text" id="website" name="website" autocomplete="off">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Nombre</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="Tu nombre"
                                required
                            >
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="<EMAIL>"
                                required
                            >
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="instagram" class="block text-sm font-medium text-gray-300 mb-1">
                                <i class="ph ph-instagram-logo mr-1"></i> Instagram <span class="text-xs text-gray-400">(opcional)</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">@</span>
                                <input
                                    type="text"
                                    id="instagram"
                                    name="instagram"
                                    class="w-full p-3 pl-8 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="usuario_instagram"
                                >
                            </div>
                        </div>

                        <div>
                            <label for="twitter" class="block text-sm font-medium text-gray-300 mb-1">
                                <i class="ph ph-twitter-logo mr-1"></i> X / Twitter <span class="text-xs text-gray-400">(opcional)</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">@</span>
                                <input
                                    type="text"
                                    id="twitter"
                                    name="twitter"
                                    class="w-full p-3 pl-8 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="usuario_twitter"
                                >
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-300 mb-1">Asunto</label>
                        <input
                            type="text"
                            id="subject"
                            name="subject"
                            class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                            placeholder="Asunto de tu mensaje"
                            required
                        >
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-300 mb-1">Mensaje</label>
                        <textarea
                            id="message"
                            name="message"
                            rows="5"
                            class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                            placeholder="Tu mensaje"
                            required
                        ></textarea>
                    </div>

                    <!-- Simple Captcha -->
                    <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                        <label class="block text-sm font-medium text-gray-300 mb-3">Verificación de seguridad</label>

                        <div class="flex items-center gap-4">
                            <div id="captchaDisplay" class="bg-gray-900 px-4 py-2 rounded font-mono text-xl tracking-widest text-orange-500 select-none"></div>
                            <button type="button" id="refreshCaptcha" class="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors">
                                <i class="ph ph-arrows-clockwise"></i>
                            </button>
                        </div>

                        <div class="mt-3">
                            <input
                                type="text"
                                id="captchaInput"
                                name="captcha"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="Ingresa el código que ves arriba"
                                required
                            >
                        </div>
                    </div>

                    <div id="formMessage" class="hidden p-4 rounded-lg"></div>

                    <div>
                        <button type="submit" class="btn btn-primary w-full">
                            <i class="ph ph-paper-plane-right"></i> Enviar Mensaje
                        </button>
                    </div>
                </form>

                <!-- Captcha and Form Submission Script -->
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialize captcha on page load
                        const captchaDisplay = document.getElementById('captchaDisplay');

                        // Function to fetch a new captcha from the server
                        function fetchNewCaptcha() {
                            fetch('/get_captcha.php')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.captcha) {
                                        captchaDisplay.textContent = data.captcha;
                                    }
                                })
                                .catch(error => {
                                    console.error('Error fetching captcha:', error);
                                    // Fallback to client-side captcha if server request fails
                                    captchaDisplay.textContent = generateClientCaptcha();
                                });
                        }

                        // Generate a client-side captcha as fallback
                        function generateClientCaptcha() {
                            const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                            let captcha = '';
                            for (let i = 0; i < 6; i++) {
                                const randomIndex = Math.floor(Math.random() * chars.length);
                                captcha += chars[randomIndex];
                            }
                            return captcha;
                        }

                        // Initial captcha load
                        fetchNewCaptcha();

                        // Refresh captcha button
                        const refreshButton = document.getElementById('refreshCaptcha');
                        refreshButton.addEventListener('click', function() {
                            fetchNewCaptcha();
                        });

                        // Form submission
                        const contactForm = document.getElementById('contactForm');
                        const formMessage = document.getElementById('formMessage');
                        const submitButton = contactForm.querySelector('button[type="submit"]');

                        contactForm.addEventListener('submit', function(e) {
                            e.preventDefault();

                            // Disable submit button and show loading state
                            submitButton.disabled = true;
                            submitButton.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Enviando...';

                            // Get form data
                            const formData = new FormData(contactForm);

                            // Send form data to server
                            fetch('/process_form.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                // Display response message
                                formMessage.textContent = data.message;
                                formMessage.className = data.success
                                    ? 'p-4 rounded-lg bg-green-900 text-white'
                                    : 'p-4 rounded-lg bg-red-900 text-white';
                                formMessage.style.display = 'block';

                                // If successful, reset form
                                if (data.success) {
                                    contactForm.reset();
                                }

                                // Update captcha if provided
                                if (data.new_captcha) {
                                    captchaDisplay.textContent = data.new_captcha;
                                } else {
                                    // Otherwise fetch a new one
                                    fetchNewCaptcha();
                                }

                                // Scroll to message
                                formMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            })
                            .catch(error => {
                                console.error('Error submitting form:', error);
                                formMessage.textContent = 'Ha ocurrido un error al enviar el formulario. Por favor, inténtalo de nuevo más tarde.';
                                formMessage.className = 'p-4 rounded-lg bg-red-900 text-white';
                                formMessage.style.display = 'block';

                                // Fetch new captcha
                                fetchNewCaptcha();
                            })
                            .finally(() => {
                                // Re-enable submit button
                                submitButton.disabled = false;
                                submitButton.innerHTML = '<i class="ph ph-paper-plane-right"></i> Enviar Mensaje';
                            });
                        });
                    });
                 </script>
             </div>
         </div>
    </div>
</section>