<?php
// Generic content page for SEO-friendly URLs
// This page displays individual content items based on their slug

// Get content from global variable set by URL router
$content = $GLOBALS['seo_content'] ?? null;

if (!$content) {
    include '404.php';
    exit;
}
?>

<style>
.content-page p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.content-page strong {
    color: #f97316;
    font-weight: 700;
}

.content-page em {
    color: #fbbf24;
    font-style: italic;
}

.content-page ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.content-page li {
    margin-bottom: 0.5rem;
    line-height: 1.7;
}

.content-page a {
    color: #f97316;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.content-page a:hover {
    color: #ea580c;
}

.content-page h1, .content-page h2, .content-page h3 {
    color: #f97316;
    margin-bottom: 1rem;
}

.content-page h1 {
    font-size: 2.5rem;
    font-weight: bold;
}

.content-page h2 {
    font-size: 2rem;
    font-weight: bold;
}

.content-page h3 {
    font-size: 1.5rem;
    font-weight: bold;
}
</style>

<section class="bg-black text-white min-h-screen py-16 px-4">
    <div class="container mx-auto max-w-4xl">
        <!-- Breadcrumbs -->
        <?php 
        $breadcrumbs = generateContentBreadcrumbs($content, 'content');
        if (count($breadcrumbs) > 1): 
        ?>
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-sm text-gray-400">
                <?php foreach ($breadcrumbs as $index => $crumb): ?>
                    <?php if ($index > 0): ?>
                        <li><i class="ph ph-caret-right text-xs"></i></li>
                    <?php endif; ?>
                    <li>
                        <?php if ($index < count($breadcrumbs) - 1): ?>
                            <a href="<?php echo $crumb['url']; ?>" class="hover:text-orange-500 transition-colors">
                                <?php echo htmlspecialchars($crumb['name']); ?>
                            </a>
                        <?php else: ?>
                            <span class="text-white"><?php echo htmlspecialchars($crumb['name']); ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ol>
        </nav>
        <?php endif; ?>

        <!-- Content Header -->
        <header class="mb-12">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                <?php echo htmlspecialchars($content['title']); ?>
            </h1>
            
            <?php if (!empty($content['subtitle'])): ?>
            <p class="text-xl text-gray-300 mb-6">
                <?php echo htmlspecialchars($content['subtitle']); ?>
            </p>
            <?php endif; ?>
            
            <!-- Meta information -->
            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-400">
                <?php if (!empty($content['publish_date'])): ?>
                <div class="flex items-center gap-1">
                    <i class="ph ph-calendar"></i>
                    <span><?php echo date('d/m/Y', strtotime($content['publish_date'])); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($content['section'])): ?>
                <div class="flex items-center gap-1">
                    <i class="ph ph-tag"></i>
                    <span class="capitalize"><?php echo htmlspecialchars($content['section']); ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($content['seo_score'])): ?>
                <div class="flex items-center gap-1">
                    <i class="ph ph-chart-line"></i>
                    <span>SEO: <?php echo $content['seo_score']; ?>/100</span>
                </div>
                <?php endif; ?>
            </div>
        </header>

        <!-- Featured Image -->
        <?php if (!empty($content['image_url'])): ?>
        <div class="mb-12">
            <img src="<?php echo htmlspecialchars($content['image_url']); ?>" 
                 alt="<?php echo htmlspecialchars($content['title']); ?>"
                 class="w-full h-64 md:h-96 object-cover rounded-xl">
        </div>
        <?php endif; ?>

        <!-- Main Content -->
        <article class="content-page">
            <div class="prose prose-lg prose-invert max-w-none">
                <?php echo $content['content']; ?>
            </div>
        </article>

        <!-- Share Section -->
        <div class="mt-16 pt-8 border-t border-gray-800">
            <h3 class="text-xl font-bold mb-4">Compartir este contenido</h3>
            <div class="flex gap-4">
                <?php 
                $current_url = getCurrentURL();
                $title = urlencode($content['title']);
                $description = urlencode($content['meta_description'] ?: substr(strip_tags($content['content']), 0, 200));
                ?>
                
                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode($current_url); ?>&text=<?php echo $title; ?>" 
                   target="_blank" rel="noopener"
                   class="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="ph ph-twitter-logo"></i>
                    <span>Twitter</span>
                </a>
                
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode($current_url); ?>" 
                   target="_blank" rel="noopener"
                   class="flex items-center gap-2 bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="ph ph-facebook-logo"></i>
                    <span>Facebook</span>
                </a>
                
                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode($current_url); ?>" 
                   target="_blank" rel="noopener"
                   class="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="ph ph-linkedin-logo"></i>
                    <span>LinkedIn</span>
                </a>
                
                <button onclick="copyToClipboard('<?php echo $current_url; ?>')" 
                        class="flex items-center gap-2 bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="ph ph-copy"></i>
                    <span>Copiar enlace</span>
                </button>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mt-16 pt-8 border-t border-gray-800">
            <div class="flex justify-between items-center">
                <a href="/" class="flex items-center gap-2 text-gray-400 hover:text-orange-500 transition-colors">
                    <i class="ph ph-arrow-left"></i>
                    <span>Volver al inicio</span>
                </a>
                
                <?php if ($content['section'] === 'manifesto' || $content['section'] === 'why_abla' || $content['section'] === 'statutes' || $content['section'] === 'why_founder' || $content['section'] === 'allies'): ?>
                <a href="/nosotros" class="flex items-center gap-2 text-gray-400 hover:text-orange-500 transition-colors">
                    <span>Ver más contenido</span>
                    <i class="ph ph-arrow-right"></i>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="ph ph-check"></i><span>¡Copiado!</span>';
        button.classList.add('bg-green-600');
        button.classList.remove('bg-gray-600');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-gray-600');
        }, 2000);
    }).catch(function(err) {
        console.error('Error al copiar: ', err);
        alert('Error al copiar el enlace');
    });
}
</script>

<?php
// Add structured data for this content
if (function_exists('getCurrentContentStructuredData')) {
    echo getCurrentContentStructuredData();
}
?>
