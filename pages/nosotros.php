<?php
// Get content from database
$db = connectDB();

// Get about content
$about = [];
$result = $db->query("SELECT * FROM content WHERE section = 'about' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $about = $result->fetch_assoc();
}

// Get manifesto content
$manifesto = [];
$result = $db->query("SELECT * FROM content WHERE section = 'manifesto' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $manifesto = $result->fetch_assoc();
}

// Get why ABLA content
$why_abla = [];
$result = $db->query("SELECT * FROM content WHERE section = 'why_abla' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $why_abla = $result->fetch_assoc();
}

// Get statutes content
$statutes = [];
$result = $db->query("SELECT * FROM content WHERE section = 'statutes' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $statutes = $result->fetch_assoc();
}

// Get why founder content
$why_founder = [];
$result = $db->query("SELECT * FROM content WHERE section = 'why_founder' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $why_founder = $result->fetch_assoc();
}

// Get allies content
$allies = [];
$result = $db->query("SELECT * FROM content WHERE section = 'allies' LIMIT 1");
if ($result && $result->num_rows > 0) {
    $allies = $result->fetch_assoc();
}

// Get team members if available
$team = [];
$result = $db->query("SELECT * FROM content WHERE section = 'team' ORDER BY id ASC");
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $team[] = $row;
    }
}
?>

<style>
.manifesto-content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.manifesto-content strong {
    color: #f97316;
    font-weight: 700;
}

.manifesto-content em {
    color: #fbbf24;
    font-style: italic;
}

.manifesto-content ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.manifesto-content li {
    margin-bottom: 0.5rem;
    line-height: 1.7;
}

.manifesto-content a {
    color: #f97316;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.manifesto-content a:hover {
    color: #ea580c;
}
</style>

<section class="bg-black text-white min-h-screen py-16 px-4">
    <div class="container mx-auto max-w-4xl">
        <!-- Título principal -->
        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
            NOSOTROS
        </h1>

        <!-- Cita en "nube" -->
        <div class="relative bg-gray-800 rounded-3xl p-6 mb-12">
            <div class="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
            <p class="text-xl italic">
                "Somos el grito manso de la descentralización en América Latina, donde las fronteras son solamente para aquellos que le temen al infinito."
            </p>
        </div>

        <!-- Navegación rápida -->
        <?php
        $sections = [];
        if (!empty($about['content'])) $sections[] = ['about', 'Sobre ABLA'];
        if (!empty($manifesto['content'])) $sections[] = ['manifesto', 'Manifiesto'];
        if (!empty($why_abla['content'])) $sections[] = ['why-abla', '¿Por qué ABLA?'];
        if (!empty($statutes['content'])) $sections[] = ['statutes', 'Estatutos ABLA'];
        if (!empty($why_founder['content'])) $sections[] = ['why-founder', '¿Por qué ser fundador?'];
        if (!empty($allies['content'])) $sections[] = ['allies', 'Aliados'];
        if (!empty($team)) $sections[] = ['team', 'Nuestro Equipo'];

        if (count($sections) > 3): ?>
        <div class="mb-12 bg-gray-900 rounded-xl p-6">
            <h3 class="text-lg font-bold mb-4 text-orange-500">Navegación rápida</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                <?php foreach ($sections as $section): ?>
                <a href="#<?php echo $section[0]; ?>" class="text-sm text-gray-300 hover:text-orange-500 transition-colors duration-200 p-2 rounded hover:bg-gray-800">
                    <?php echo $section[1]; ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección Sobre Nosotros -->
        <div id="about" class="mb-16">
            <h2 class="text-3xl font-bold mb-6">Sobre ABLA</h2>
            <div class="text-gray-300 space-y-4">
                <?php if (!empty($about['content'])): ?>
                    <?php echo $about['content']; ?>
                <?php else: ?>
                    <p>
                        La Asociación Bitcoin Latinoamérica (ABLA) es una organización sin fines de lucro dedicada a promover la adopción, educación y desarrollo del ecosistema Bitcoin en América Latina.
                    </p>
                    <p>
                        Nuestra misión es empoderar a las comunidades latinoamericanas a través de la tecnología Bitcoin, fomentando la inclusión financiera, la soberanía individual y el desarrollo económico en la región.
                    </p>
                    <p>
                        Trabajamos en colaboración con iniciativas locales en diferentes países de América Latina, apoyando proyectos educativos, de investigación y desarrollo que contribuyan a la adopción de Bitcoin.
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sección Nuestros Valores -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold mb-6">Nuestros Valores</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gray-900 p-6 rounded-xl">
                    <h3 class="text-xl font-bold mb-3 text-orange-500">Descentralización</h3>
                    <p class="text-gray-300">
                        Promovemos la descentralización como principio fundamental para la libertad financiera y la resistencia a la censura.
                    </p>
                </div>
                <div class="bg-gray-900 p-6 rounded-xl">
                    <h3 class="text-xl font-bold mb-3 text-orange-500">Educación</h3>
                    <p class="text-gray-300">
                        Creemos en el poder transformador de la educación para empoderar a las personas y comunidades.
                    </p>
                </div>
                <div class="bg-gray-900 p-6 rounded-xl">
                    <h3 class="text-xl font-bold mb-3 text-orange-500">Colaboración</h3>
                    <p class="text-gray-300">
                        Fomentamos la colaboración entre iniciativas y comunidades para maximizar el impacto positivo en la región.
                    </p>
                </div>
            </div>
        </div>

        <!-- Sección Manifiesto -->
        <?php if (!empty($manifesto['content'])): ?>
        <div id="manifesto" class="mb-16">
            <h2 class="text-3xl font-bold mb-6 text-orange-500"><?php echo !empty($manifesto['title']) ? $manifesto['title'] : 'Manifiesto'; ?></h2>
            <div class="text-gray-300 space-y-4 leading-relaxed text-lg manifesto-content">
                <?php echo $manifesto['content']; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección ¿Por qué ABLA? -->
        <?php if (!empty($why_abla['content'])): ?>
        <div id="why-abla" class="mb-16">
            <h2 class="text-3xl font-bold mb-6 text-orange-500"><?php echo !empty($why_abla['title']) ? $why_abla['title'] : '¿Por qué ABLA?'; ?></h2>
            <div class="text-gray-300 space-y-4 leading-relaxed text-lg manifesto-content">
                <?php echo $why_abla['content']; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección Estatutos ABLA -->
        <?php if (!empty($statutes['content'])): ?>
        <div id="statutes" class="mb-16">
            <h2 class="text-3xl font-bold mb-6 text-orange-500"><?php echo !empty($statutes['title']) ? $statutes['title'] : 'Estatutos ABLA'; ?></h2>
            <div class="text-gray-300 space-y-4 leading-relaxed text-lg manifesto-content">
                <?php echo $statutes['content']; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección ¿Por qué ser fundador? -->
        <?php if (!empty($why_founder['content'])): ?>
        <div id="why-founder" class="mb-16">
            <h2 class="text-3xl font-bold mb-6 text-orange-500"><?php echo !empty($why_founder['title']) ? $why_founder['title'] : '¿Por qué ser fundador?'; ?></h2>
            <div class="text-gray-300 space-y-4 leading-relaxed text-lg manifesto-content">
                <?php echo $why_founder['content']; ?>
            </div>

            <!-- Botón para formar parte de ABLA -->
            <div class="mt-8 text-center">
                <a href="/?route=sumate" class="inline-block bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    Formá parte de ABLA
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección Aliados -->
        <?php if (!empty($allies['content'])): ?>
        <div id="allies" class="mb-16">
            <h2 class="text-3xl font-bold mb-6 text-orange-500"><?php echo !empty($allies['title']) ? $allies['title'] : 'Aliados'; ?></h2>
            <div class="text-gray-300 space-y-4 leading-relaxed text-lg manifesto-content">
                <?php echo $allies['content']; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sección Equipo -->
        <?php if (!empty($team)): ?>
        <div id="team">
            <h2 class="text-3xl font-bold mb-6">Nuestro Equipo</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($team as $member): ?>
                <div class="bg-gray-900 p-6 rounded-xl text-center">
                    <?php if (!empty($member['image_url'])): ?>
                    <div class="w-32 h-32 mx-auto mb-4 overflow-hidden rounded-full">
                        <img src="<?php echo $member['image_url']; ?>" alt="<?php echo $member['title']; ?>" class="w-full h-full object-cover">
                    </div>
                    <?php endif; ?>
                    <h3 class="text-xl font-bold"><?php echo $member['title']; ?></h3>
                    <?php if (!empty($member['subtitle'])): ?>
                    <p class="text-orange-500 mb-2"><?php echo $member['subtitle']; ?></p>
                    <?php endif; ?>
                    <?php if (!empty($member['content'])): ?>
                    <p class="text-gray-300 text-sm"><?php echo $member['content']; ?></p>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
